{"Bug Investigation": {"task": "Investigate potential memory leaks in the file processing pipeline", "task_type": "debugging", "total_iterations": 4, "overall_confidence": 0.34762820512820514, "entity_summaries": {"io.format_files_for_input": {"final_confidence": 0.4666666666666667, "total_iterations": 4, "insights": ["Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation"], "suggested_next_steps": []}, "context_request_handler.process_context_request": {"final_confidence": 0.3666666666666667, "total_iterations": 4, "insights": ["Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io"], "open_issues": ["Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.__init__": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation"], "suggested_next_steps": []}, "commands.cmd_lint": {"final_confidence": 0.3516666666666667, "total_iterations": 4, "insights": ["Function cmd_lint has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function cmd_lint has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function cmd_lint has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function cmd_lint has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io"], "open_issues": ["Low confidence analysis for commands.cmd_lint", "Needs deeper investigation", "Low confidence analysis for commands.cmd_lint", "Needs deeper investigation", "Low confidence analysis for commands.cmd_lint", "Needs deeper investigation", "Low confidence analysis for commands.cmd_lint", "Needs deeper investigation"], "suggested_next_steps": []}, "aider_integration_service.get_symbol_references_between_files": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file"], "open_issues": ["Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.__init__": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.add_requested_file": {"final_confidence": 0.39166666666666666, "total_iterations": 4, "insights": ["Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.format_chat_chunks": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container"], "open_issues": ["Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.get_repo_map": {"final_confidence": 0.38666666666666666, "total_iterations": 4, "insights": ["Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation"], "suggested_next_steps": []}, "commands.cmd_tokens": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function cmd_tokens has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function cmd_tokens has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function cmd_tokens has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function cmd_tokens has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function cmd_tokens has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function cmd_tokens has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function cmd_tokens has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file"], "open_issues": ["Low confidence analysis for commands.cmd_tokens", "Needs deeper investigation", "Low confidence analysis for commands.cmd_tokens", "Needs deeper investigation", "Low confidence analysis for commands.cmd_tokens", "Needs deeper investigation", "Low confidence analysis for commands.cmd_tokens", "Needs deeper investigation", "Low confidence analysis for commands.cmd_tokens", "Needs deeper investigation", "Low confidence analysis for commands.cmd_tokens", "Needs deeper investigation", "Low confidence analysis for commands.cmd_tokens", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_map_requests": {"final_confidence": 0.33666666666666667, "total_iterations": 4, "insights": ["Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "commands.cmd_drop": {"final_confidence": 0.38666666666666666, "total_iterations": 4, "insights": ["Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state"], "open_issues": ["Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation"], "suggested_next_steps": []}, "debug_search_issue.debug_search_issue": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function debug_search_issue has 15 dependencies", "Has side effects: network_io, writes_log, modifies_file", "Function debug_search_issue has 15 dependencies", "Has side effects: network_io, writes_log, modifies_file", "Function debug_search_issue has 15 dependencies", "Has side effects: network_io, writes_log, modifies_file", "Function debug_search_issue has 15 dependencies", "Has side effects: network_io, writes_log, modifies_file"], "open_issues": ["Low confidence analysis for debug_search_issue.debug_search_issue", "Needs deeper investigation", "Low confidence analysis for debug_search_issue.debug_search_issue", "Needs deeper investigation", "Low confidence analysis for debug_search_issue.debug_search_issue", "Needs deeper investigation", "Low confidence analysis for debug_search_issue.debug_search_issue", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_file_mentions": {"final_confidence": 0.33666666666666667, "total_iterations": 4, "insights": ["Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container"], "open_issues": ["Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_repo_map": {"final_confidence": 0.38666666666666666, "total_iterations": 4, "insights": ["Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation"], "suggested_next_steps": []}, "repo.get_tracked_files": {"final_confidence": 0.3566666666666667, "total_iterations": 4, "insights": ["Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.process_file_requests": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.get_file_mentions": {"final_confidence": 0.33666666666666667, "total_iterations": 4, "insights": ["Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container"], "open_issues": ["Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.format_chat_chunks": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container"], "open_issues": ["Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.get_announcements": {"final_confidence": 0.3666666666666667, "total_iterations": 4, "insights": ["Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_context_requests": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_file_requests": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.add_requested_file": {"final_confidence": 0.39166666666666666, "total_iterations": 4, "insights": ["Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_announcements": {"final_confidence": 0.3666666666666667, "total_iterations": 4, "insights": ["Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.process_context_requests": {"final_confidence": 0.32666666666666666, "total_iterations": 4, "insights": ["Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "commands.cmd_add": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation"], "suggested_next_steps": []}}, "global_insights": ["Has side effects: network_io, modifies_state", "Has side effects: network_io, modifies_state, modifies_container", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_request has 9 dependencies", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Has side effects: network_io, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Function get_repo_map has 7 dependencies", "Function debug_search_issue has 15 dependencies", "Function get_symbol_references_between_files has 15 dependencies", "Function __init__ has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_context_requests has 15 dependencies", "Function process_map_requests has 12 dependencies", "Function process_context_requests has 13 dependencies", "Function cmd_add has 15 dependencies", "Has side effects: network_io, writes_log, modifies_file", "Function process_file_requests has 15 dependencies", "Function cmd_drop has 12 dependencies", "Function cmd_lint has 13 dependencies", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function cmd_tokens has 15 dependencies"], "analysis_summary": {"total_entities_analyzed": 26, "total_iterations": 4, "low_confidence_count": 26, "high_confidence_count": 0, "global_insights_count": 208, "last_analysis": "2025-05-28T22:59:11.214064+00:00"}, "confidence_summary": {"average_confidence": 0.34762820512820514, "min_confidence": 0.31666666666666665, "max_confidence": 0.4666666666666667, "low_confidence_count": 26, "total_entities": 26}, "iteration_history": [{"iteration": 1, "timestamp": "2025-05-28T22:59:09.723279+00:00", "entities_count": 26, "confidence": 0.3176282051282051, "status": "continue"}, {"iteration": 2, "timestamp": "2025-05-28T22:59:10.156899+00:00", "entities_count": 26, "confidence": 0.3176282051282051, "status": "continue"}, {"iteration": 3, "timestamp": "2025-05-28T22:59:10.493744+00:00", "entities_count": 26, "confidence": 0.34762820512820514, "status": "continue"}, {"iteration": 4, "timestamp": "2025-05-28T22:59:11.214064+00:00", "entities_count": 26, "confidence": 0.34762820512820514, "status": "continue"}], "analysis_metadata": {"project_path": "C:\\Users\\<USER>\\Documents\\aider_project\\aider__500", "max_tokens": 4000, "engine_version": "IAA Protocol v1.0", "analysis_type": "multi_turn_reasoning"}}, "Feature Development": {"task": "Analyze the architecture for adding real-time collaboration features", "task_type": "feature_development", "total_iterations": 3, "overall_confidence": 0.35012820512820514, "entity_summaries": {"io.format_files_for_input": {"final_confidence": 0.4666666666666667, "total_iterations": 7, "insights": ["Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation"], "suggested_next_steps": []}, "context_request_handler.process_context_request": {"final_confidence": 0.3666666666666667, "total_iterations": 7, "insights": ["Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io"], "open_issues": ["Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.__init__": {"final_confidence": 0.31666666666666665, "total_iterations": 7, "insights": ["Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation"], "suggested_next_steps": []}, "aider_integration_service.get_symbol_references_between_files": {"final_confidence": 0.31666666666666665, "total_iterations": 7, "insights": ["Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file"], "open_issues": ["Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.__init__": {"final_confidence": 0.31666666666666665, "total_iterations": 7, "insights": ["Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.add_requested_file": {"final_confidence": 0.39166666666666666, "total_iterations": 7, "insights": ["Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation"], "suggested_next_steps": []}, "repomap.get_repo_overview": {"final_confidence": 0.3666666666666667, "total_iterations": 3, "insights": ["Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container"], "open_issues": ["Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation", "Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation", "Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation", "Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation", "Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation", "Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation", "Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation", "Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.format_chat_chunks": {"final_confidence": 0.31666666666666665, "total_iterations": 7, "insights": ["Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container"], "open_issues": ["Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.get_repo_map": {"final_confidence": 0.38666666666666666, "total_iterations": 7, "insights": ["Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation"], "suggested_next_steps": []}, "commands.cmd_tokens": {"final_confidence": 0.31666666666666665, "total_iterations": 7, "insights": ["Function cmd_tokens has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function cmd_tokens has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function cmd_tokens has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function cmd_tokens has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function cmd_tokens has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function cmd_tokens has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function cmd_tokens has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file"], "open_issues": ["Low confidence analysis for commands.cmd_tokens", "Needs deeper investigation", "Low confidence analysis for commands.cmd_tokens", "Needs deeper investigation", "Low confidence analysis for commands.cmd_tokens", "Needs deeper investigation", "Low confidence analysis for commands.cmd_tokens", "Needs deeper investigation", "Low confidence analysis for commands.cmd_tokens", "Needs deeper investigation", "Low confidence analysis for commands.cmd_tokens", "Needs deeper investigation", "Low confidence analysis for commands.cmd_tokens", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_map_requests": {"final_confidence": 0.33666666666666667, "total_iterations": 7, "insights": ["Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "commands.cmd_drop": {"final_confidence": 0.38666666666666666, "total_iterations": 7, "insights": ["Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state"], "open_issues": ["Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_file_mentions": {"final_confidence": 0.33666666666666667, "total_iterations": 7, "insights": ["Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container"], "open_issues": ["Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_repo_map": {"final_confidence": 0.38666666666666666, "total_iterations": 7, "insights": ["Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation"], "suggested_next_steps": []}, "repo.get_tracked_files": {"final_confidence": 0.3566666666666667, "total_iterations": 7, "insights": ["Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.process_file_requests": {"final_confidence": 0.31666666666666665, "total_iterations": 7, "insights": ["Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.get_file_mentions": {"final_confidence": 0.33666666666666667, "total_iterations": 7, "insights": ["Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container"], "open_issues": ["Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.format_chat_chunks": {"final_confidence": 0.31666666666666665, "total_iterations": 7, "insights": ["Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container"], "open_issues": ["Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.get_announcements": {"final_confidence": 0.3666666666666667, "total_iterations": 7, "insights": ["Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_context_requests": {"final_confidence": 0.31666666666666665, "total_iterations": 7, "insights": ["Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_file_requests": {"final_confidence": 0.31666666666666665, "total_iterations": 7, "insights": ["Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.add_requested_file": {"final_confidence": 0.39166666666666666, "total_iterations": 7, "insights": ["Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_announcements": {"final_confidence": 0.3666666666666667, "total_iterations": 7, "insights": ["Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.process_context_requests": {"final_confidence": 0.32666666666666666, "total_iterations": 7, "insights": ["Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "mdstream.update": {"final_confidence": 0.3666666666666667, "total_iterations": 3, "insights": ["Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for mdstream.update", "Needs deeper investigation", "Low confidence analysis for mdstream.update", "Needs deeper investigation", "Low confidence analysis for mdstream.update", "Needs deeper investigation", "Low confidence analysis for mdstream.update", "Needs deeper investigation", "Low confidence analysis for mdstream.update", "Needs deeper investigation", "Low confidence analysis for mdstream.update", "Needs deeper investigation", "Low confidence analysis for mdstream.update", "Needs deeper investigation", "Low confidence analysis for mdstream.update", "Needs deeper investigation"], "suggested_next_steps": []}, "commands.cmd_add": {"final_confidence": 0.31666666666666665, "total_iterations": 7, "insights": ["Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation"], "suggested_next_steps": []}}, "global_insights": ["Has side effects: network_io, modifies_state", "Has side effects: network_io, modifies_state, modifies_container", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_request has 9 dependencies", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_symbol_references_between_files has 15 dependencies", "Function __init__ has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_context_requests has 15 dependencies", "Function process_map_requests has 12 dependencies", "Function process_context_requests has 13 dependencies", "Function cmd_add has 15 dependencies", "Function process_file_requests has 15 dependencies", "Function cmd_drop has 12 dependencies", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function cmd_tokens has 15 dependencies"], "analysis_summary": {"total_entities_analyzed": 28, "total_iterations": 7, "low_confidence_count": 28, "high_confidence_count": 0, "global_insights_count": 364, "last_analysis": "2025-05-28T22:59:12.491821+00:00"}, "confidence_summary": {"average_confidence": 0.3489880952380952, "min_confidence": 0.31666666666666665, "max_confidence": 0.4666666666666667, "low_confidence_count": 28, "total_entities": 28}, "iteration_history": [{"iteration": 1, "timestamp": "2025-05-28T22:59:11.601539+00:00", "entities_count": 26, "confidence": 0.32012820512820517, "status": "continue"}, {"iteration": 2, "timestamp": "2025-05-28T22:59:11.997868+00:00", "entities_count": 26, "confidence": 0.32012820512820517, "status": "continue"}, {"iteration": 3, "timestamp": "2025-05-28T22:59:12.491821+00:00", "entities_count": 26, "confidence": 0.35012820512820514, "status": "continue"}], "analysis_metadata": {"project_path": "C:\\Users\\<USER>\\Documents\\aider_project\\aider__500", "max_tokens": 4000, "engine_version": "IAA Protocol v1.0", "analysis_type": "multi_turn_reasoning"}}, "Code Refactoring": {"task": "Identify opportunities to refactor the context selection logic for better modularity", "task_type": "refactoring", "total_iterations": 3, "overall_confidence": 0.3507051282051282, "entity_summaries": {"io.format_files_for_input": {"final_confidence": 0.4666666666666667, "total_iterations": 10, "insights": ["Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation"], "suggested_next_steps": []}, "context_request_handler.process_context_request": {"final_confidence": 0.3666666666666667, "total_iterations": 10, "insights": ["Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io"], "open_issues": ["Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.__init__": {"final_confidence": 0.31666666666666665, "total_iterations": 10, "insights": ["Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation"], "suggested_next_steps": []}, "aider_integration_service.get_symbol_references_between_files": {"final_confidence": 0.31666666666666665, "total_iterations": 10, "insights": ["Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file"], "open_issues": ["Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.__init__": {"final_confidence": 0.31666666666666665, "total_iterations": 10, "insights": ["Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.add_requested_file": {"final_confidence": 0.39166666666666666, "total_iterations": 10, "insights": ["Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation"], "suggested_next_steps": []}, "repomap.get_repo_overview": {"final_confidence": 0.3666666666666667, "total_iterations": 6, "insights": ["Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container"], "open_issues": ["Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation", "Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation", "Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation", "Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation", "Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation", "Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation", "Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation", "Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.create": {"final_confidence": 0.38666666666666666, "total_iterations": 2, "insights": ["Function create has 7 dependencies", "Has side effects: database_io, writes_log", "Function create has 7 dependencies", "Has side effects: database_io, writes_log", "Function create has 7 dependencies", "Has side effects: database_io, writes_log", "Function create has 7 dependencies", "Has side effects: database_io, writes_log"], "open_issues": ["Low confidence analysis for base_coder.create", "Needs deeper investigation", "Low confidence analysis for base_coder.create", "Needs deeper investigation", "Low confidence analysis for base_coder.create", "Needs deeper investigation", "Low confidence analysis for base_coder.create", "Needs deeper investigation"], "suggested_next_steps": []}, "analytics.event": {"final_confidence": 0.39166666666666666, "total_iterations": 3, "insights": ["Function event has 9 dependencies", "Has side effects: database_io, modifies_file, modifies_container, modifies_state", "Function event has 9 dependencies", "Has side effects: database_io, modifies_file, modifies_container, modifies_state", "Function event has 9 dependencies", "Has side effects: database_io, modifies_file, modifies_container, modifies_state", "Function event has 9 dependencies", "Has side effects: database_io, modifies_file, modifies_container, modifies_state", "Function event has 9 dependencies", "Has side effects: database_io, modifies_file, modifies_container, modifies_state"], "open_issues": ["Low confidence analysis for analytics.event", "Needs deeper investigation", "Low confidence analysis for analytics.event", "Needs deeper investigation", "Low confidence analysis for analytics.event", "Needs deeper investigation", "Low confidence analysis for analytics.event", "Needs deeper investigation", "Low confidence analysis for analytics.event", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.format_chat_chunks": {"final_confidence": 0.31666666666666665, "total_iterations": 10, "insights": ["Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container"], "open_issues": ["Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation"], "suggested_next_steps": []}, "test_real_context_request_fix.main": {"final_confidence": 0.6766666666666666, "total_iterations": 1, "insights": ["Function main has 1 dependencies", "Has side effects: network_io, writes_log"], "open_issues": [], "suggested_next_steps": []}, "base_coder.process_map_requests": {"final_confidence": 0.33666666666666667, "total_iterations": 10, "insights": ["Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "commands.cmd_drop": {"final_confidence": 0.38666666666666666, "total_iterations": 10, "insights": ["Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state"], "open_issues": ["Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation", "Low confidence analysis for commands.cmd_drop", "Needs deeper investigation"], "suggested_next_steps": []}, "commands.cmd_add": {"final_confidence": 0.31666666666666665, "total_iterations": 10, "insights": ["Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function cmd_add has 15 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation", "Low confidence analysis for commands.cmd_add", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_file_mentions": {"final_confidence": 0.33666666666666667, "total_iterations": 10, "insights": ["Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container"], "open_issues": ["Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation"], "suggested_next_steps": []}, "repo.get_tracked_files": {"final_confidence": 0.3566666666666667, "total_iterations": 10, "insights": ["Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.process_file_requests": {"final_confidence": 0.31666666666666665, "total_iterations": 10, "insights": ["Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.get_file_mentions": {"final_confidence": 0.33666666666666667, "total_iterations": 10, "insights": ["Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container"], "open_issues": ["Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.format_chat_chunks": {"final_confidence": 0.31666666666666665, "total_iterations": 10, "insights": ["Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container"], "open_issues": ["Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.get_announcements": {"final_confidence": 0.3666666666666667, "total_iterations": 10, "insights": ["Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_context_requests": {"final_confidence": 0.31666666666666665, "total_iterations": 10, "insights": ["Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "context_request_handler.parse_context_request": {"final_confidence": 0.32666666666666666, "total_iterations": 3, "insights": ["Function parse_context_request has 13 dependencies", "Has side effects: network_io, writes_log", "Function parse_context_request has 13 dependencies", "Has side effects: network_io, writes_log", "Function parse_context_request has 13 dependencies", "Has side effects: network_io, writes_log", "Function parse_context_request has 13 dependencies", "Has side effects: network_io, writes_log", "Function parse_context_request has 13 dependencies", "Has side effects: network_io, writes_log"], "open_issues": ["Low confidence analysis for context_request_handler.parse_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.parse_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.parse_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.parse_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.parse_context_request", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.add_requested_file": {"final_confidence": 0.39166666666666666, "total_iterations": 10, "insights": ["Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_announcements": {"final_confidence": 0.3666666666666667, "total_iterations": 10, "insights": ["Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation"], "suggested_next_steps": []}, "test_hidden_context_detection.test_all_possible_context_leaks": {"final_confidence": 0.5216666666666666, "total_iterations": 1, "insights": ["Function test_all_possible_context_leaks has 8 dependencies", "Has side effects: network_io, writes_log"], "open_issues": ["Low confidence analysis for test_hidden_context_detection.test_all_possible_context_leaks", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.process_context_requests": {"final_confidence": 0.32666666666666666, "total_iterations": 10, "insights": ["Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "mdstream.update": {"final_confidence": 0.3666666666666667, "total_iterations": 6, "insights": ["Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for mdstream.update", "Needs deeper investigation", "Low confidence analysis for mdstream.update", "Needs deeper investigation", "Low confidence analysis for mdstream.update", "Needs deeper investigation", "Low confidence analysis for mdstream.update", "Needs deeper investigation", "Low confidence analysis for mdstream.update", "Needs deeper investigation", "Low confidence analysis for mdstream.update", "Needs deeper investigation", "Low confidence analysis for mdstream.update", "Needs deeper investigation", "Low confidence analysis for mdstream.update", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_file_requests": {"final_confidence": 0.31666666666666665, "total_iterations": 10, "insights": ["Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation"], "suggested_next_steps": []}}, "global_insights": ["Has side effects: database_io, writes_log", "Has side effects: network_io, modifies_state", "Has side effects: network_io, writes_log", "Has side effects: network_io, modifies_state, modifies_container", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_request has 9 dependencies", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: database_io, network_io, modifies_file, writes_log, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function test_all_possible_context_leaks has 8 dependencies", "Function event has 9 dependencies", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_symbol_references_between_files has 15 dependencies", "Function __init__ has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function main has 1 dependencies", "Function get_file_mentions has 12 dependencies", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_context_requests has 15 dependencies", "Function parse_context_request has 13 dependencies", "Function create has 7 dependencies", "Function process_context_requests has 13 dependencies", "Function process_map_requests has 12 dependencies", "Function cmd_add has 15 dependencies", "Function process_file_requests has 15 dependencies", "Function cmd_drop has 12 dependencies", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Has side effects: database_io, modifies_file, modifies_container, modifies_state"], "analysis_summary": {"total_entities_analyzed": 33, "total_iterations": 10, "low_confidence_count": 32, "high_confidence_count": 0, "global_insights_count": 522, "last_analysis": "2025-05-28T22:59:14.238471+00:00"}, "confidence_summary": {"average_confidence": 0.3659090909090909, "min_confidence": 0.31666666666666665, "max_confidence": 0.6766666666666666, "low_confidence_count": 33, "total_entities": 33}, "iteration_history": [{"iteration": 1, "timestamp": "2025-05-28T22:59:13.147503+00:00", "entities_count": 27, "confidence": 0.33999999999999997, "status": "continue"}, {"iteration": 2, "timestamp": "2025-05-28T22:59:13.672629+00:00", "entities_count": 26, "confidence": 0.3207051282051282, "status": "continue"}, {"iteration": 3, "timestamp": "2025-05-28T22:59:14.238471+00:00", "entities_count": 26, "confidence": 0.3507051282051282, "status": "continue"}], "analysis_metadata": {"project_path": "C:\\Users\\<USER>\\Documents\\aider_project\\aider__500", "max_tokens": 4000, "engine_version": "IAA Protocol v1.0", "analysis_type": "multi_turn_reasoning"}}, "Documentation Analysis": {"task": "Assess documentation coverage and identify areas needing better documentation", "task_type": "documentation", "total_iterations": 2, "overall_confidence": 0.3233974358974359, "entity_summaries": {"io.format_files_for_input": {"final_confidence": 0.4366666666666667, "total_iterations": 12, "insights": ["Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation", "Low confidence analysis for io.format_files_for_input", "Needs deeper investigation"], "suggested_next_steps": []}, "context_request_handler.process_context_request": {"final_confidence": 0.33666666666666667, "total_iterations": 12, "insights": ["Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Function process_context_request has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io"], "open_issues": ["Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.__init__": {"final_confidence": 0.2866666666666667, "total_iterations": 12, "insights": ["Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder.__init__", "Needs deeper investigation"], "suggested_next_steps": []}, "aider_integration_service.get_symbol_references_between_files": {"final_confidence": 0.2866666666666667, "total_iterations": 12, "insights": ["Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_file"], "open_issues": ["Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation", "Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.__init__": {"final_confidence": 0.2866666666666667, "total_iterations": 12, "insights": ["Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation", "Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.add_requested_file": {"final_confidence": 0.3616666666666667, "total_iterations": 12, "insights": ["Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.create": {"final_confidence": 0.3566666666666667, "total_iterations": 4, "insights": ["Function create has 7 dependencies", "Has side effects: database_io, writes_log", "Function create has 7 dependencies", "Has side effects: database_io, writes_log", "Function create has 7 dependencies", "Has side effects: database_io, writes_log", "Function create has 7 dependencies", "Has side effects: database_io, writes_log"], "open_issues": ["Low confidence analysis for base_coder.create", "Needs deeper investigation", "Low confidence analysis for base_coder.create", "Needs deeper investigation", "Low confidence analysis for base_coder.create", "Needs deeper investigation", "Low confidence analysis for base_coder.create", "Needs deeper investigation"], "suggested_next_steps": []}, "repomap.get_repo_overview": {"final_confidence": 0.33666666666666667, "total_iterations": 8, "insights": ["Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container"], "open_issues": ["Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation", "Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation", "Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation", "Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation", "Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation", "Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation", "Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation", "Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation"], "suggested_next_steps": []}, "analytics.event": {"final_confidence": 0.3616666666666667, "total_iterations": 5, "insights": ["Function event has 9 dependencies", "Has side effects: database_io, modifies_file, modifies_container, modifies_state", "Function event has 9 dependencies", "Has side effects: database_io, modifies_file, modifies_container, modifies_state", "Function event has 9 dependencies", "Has side effects: database_io, modifies_file, modifies_container, modifies_state", "Function event has 9 dependencies", "Has side effects: database_io, modifies_file, modifies_container, modifies_state", "Function event has 9 dependencies", "Has side effects: database_io, modifies_file, modifies_container, modifies_state"], "open_issues": ["Low confidence analysis for analytics.event", "Needs deeper investigation", "Low confidence analysis for analytics.event", "Needs deeper investigation", "Low confidence analysis for analytics.event", "Needs deeper investigation", "Low confidence analysis for analytics.event", "Needs deeper investigation", "Low confidence analysis for analytics.event", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.format_chat_chunks": {"final_confidence": 0.2866666666666667, "total_iterations": 12, "insights": ["Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container"], "open_issues": ["Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.get_repo_map": {"final_confidence": 0.3566666666666667, "total_iterations": 9, "insights": ["Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_map_requests": {"final_confidence": 0.3066666666666667, "total_iterations": 12, "insights": ["Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_map_requests has 12 dependencies", "Has side effects: network_io, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_file_mentions": {"final_confidence": 0.3066666666666667, "total_iterations": 12, "insights": ["Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container"], "open_issues": ["Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_repo_map": {"final_confidence": 0.3566666666666667, "total_iterations": 9, "insights": ["Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Function get_repo_map has 7 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation"], "suggested_next_steps": []}, "repo.get_tracked_files": {"final_confidence": 0.32666666666666666, "total_iterations": 12, "insights": ["Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation", "Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.process_file_requests": {"final_confidence": 0.2866666666666667, "total_iterations": 12, "insights": ["Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.get_file_mentions": {"final_confidence": 0.3066666666666667, "total_iterations": 12, "insights": ["Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container", "Function get_file_mentions has 12 dependencies", "Has side effects: network_io, modifies_state, modifies_container"], "open_issues": ["Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation", "Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.format_chat_chunks": {"final_confidence": 0.2866666666666667, "total_iterations": 12, "insights": ["Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container"], "open_issues": ["Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation", "Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_context_requests": {"final_confidence": 0.2866666666666667, "total_iterations": 12, "insights": ["Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.get_announcements": {"final_confidence": 0.33666666666666667, "total_iterations": 12, "insights": ["Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation"], "suggested_next_steps": []}, "context_request_handler.parse_context_request": {"final_confidence": 0.29666666666666663, "total_iterations": 5, "insights": ["Function parse_context_request has 13 dependencies", "Has side effects: network_io, writes_log", "Function parse_context_request has 13 dependencies", "Has side effects: network_io, writes_log", "Function parse_context_request has 13 dependencies", "Has side effects: network_io, writes_log", "Function parse_context_request has 13 dependencies", "Has side effects: network_io, writes_log", "Function parse_context_request has 13 dependencies", "Has side effects: network_io, writes_log"], "open_issues": ["Low confidence analysis for context_request_handler.parse_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.parse_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.parse_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.parse_context_request", "Needs deeper investigation", "Low confidence analysis for context_request_handler.parse_context_request", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.add_requested_file": {"final_confidence": 0.3616666666666667, "total_iterations": 12, "insights": ["Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, modifies_container, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation", "Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_announcements": {"final_confidence": 0.33666666666666667, "total_iterations": 12, "insights": ["Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation", "Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.process_context_requests": {"final_confidence": 0.29666666666666663, "total_iterations": 12, "insights": ["Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function process_context_requests has 13 dependencies", "Has side effects: network_io, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation", "Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "mdstream.update": {"final_confidence": 0.33666666666666667, "total_iterations": 8, "insights": ["Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state"], "open_issues": ["Low confidence analysis for mdstream.update", "Needs deeper investigation", "Low confidence analysis for mdstream.update", "Needs deeper investigation", "Low confidence analysis for mdstream.update", "Needs deeper investigation", "Low confidence analysis for mdstream.update", "Needs deeper investigation", "Low confidence analysis for mdstream.update", "Needs deeper investigation", "Low confidence analysis for mdstream.update", "Needs deeper investigation", "Low confidence analysis for mdstream.update", "Needs deeper investigation", "Low confidence analysis for mdstream.update", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_file_requests": {"final_confidence": 0.2866666666666667, "total_iterations": 12, "insights": ["Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state"], "open_issues": ["Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation", "Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation"], "suggested_next_steps": []}}, "global_insights": ["Has side effects: database_io, writes_log", "Has side effects: network_io, modifies_state", "Has side effects: network_io, writes_log", "Has side effects: network_io, modifies_state, modifies_container", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_request has 9 dependencies", "Function format_files_for_input has 9 dependencies", "Has side effects: network_io, database_io, modifies_file, writes_log, modifies_state", "Function update has 9 dependencies", "Has side effects: network_io, modifies_global, database_io, modifies_state", "Has side effects: network_io, writes_log, modifies_state, modifies_file", "Function get_announcements has 9 dependencies", "Function event has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state", "Function get_repo_overview has 9 dependencies", "Function add_requested_file has 9 dependencies", "Has side effects: network_io, writes_log, modifies_state, modifies_container", "Function get_repo_map has 7 dependencies", "Function get_symbol_references_between_files has 15 dependencies", "Function __init__ has 15 dependencies", "Has side effects: network_io, writes_log, modifies_state, database_io", "Has side effects: network_io, database_io, modifies_container, writes_log, modifies_state", "Has side effects: network_io, modifies_container, writes_log, modifies_state", "Function get_file_mentions has 12 dependencies", "Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_context_requests has 15 dependencies", "Function create has 7 dependencies", "Function parse_context_request has 13 dependencies", "Function process_map_requests has 12 dependencies", "Function process_context_requests has 13 dependencies", "Function process_file_requests has 15 dependencies", "Function format_chat_chunks has 14 dependencies", "Has side effects: network_io, modifies_state, modifies_file, modifies_container", "Has side effects: database_io, modifies_file, modifies_container, modifies_state"], "analysis_summary": {"total_entities_analyzed": 33, "total_iterations": 12, "low_confidence_count": 32, "high_confidence_count": 0, "global_insights_count": 626, "last_analysis": "2025-05-28T22:59:14.841699+00:00"}, "confidence_summary": {"average_confidence": 0.3422727272727273, "min_confidence": 0.2866666666666667, "max_confidence": 0.6766666666666666, "low_confidence_count": 33, "total_entities": 33}, "iteration_history": [{"iteration": 1, "timestamp": "2025-05-28T22:59:14.460968+00:00", "entities_count": 26, "confidence": 0.3233974358974359, "status": "continue"}, {"iteration": 2, "timestamp": "2025-05-28T22:59:14.841699+00:00", "entities_count": 26, "confidence": 0.3233974358974359, "status": "continue"}], "analysis_metadata": {"project_path": "C:\\Users\\<USER>\\Documents\\aider_project\\aider__500", "max_tokens": 4000, "engine_version": "IAA Protocol v1.0", "analysis_type": "multi_turn_reasoning"}}}