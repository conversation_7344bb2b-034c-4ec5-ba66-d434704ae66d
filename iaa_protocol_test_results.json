{"Bug Investigation": {"task": "Investigate potential memory leaks in the file processing pipeline", "task_type": "debugging", "total_iterations": 4, "overall_confidence": 0.34762820512820514, "entity_summaries": {"base_coder.get_repo_map": {"final_confidence": 0.38666666666666666, "total_iterations": 4, "insights": ["Function get_repo_map has 7 dependencies", "Has side effects: network_io, database_io, modifies_global, modifies_state", "Function get_repo_map complexity analysis: 17 total connections", "Potential issues detected: TypeError, ImportError, AttributeError", "Function get_repo_map architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation"], "suggested_next_steps": []}, "commands.cmd_tokens": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function cmd_tokens has 15 dependencies", "Has side effects: modifies_state, modifies_file, network_io, writes_log", "Function cmd_tokens complexity analysis: 16 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, ValueError, AttributeError", "Function cmd_tokens architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for commands.cmd_tokens", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_file_requests": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests complexity analysis: 19 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function process_file_requests architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.add_requested_file": {"final_confidence": 0.39166666666666666, "total_iterations": 4, "insights": ["Function add_requested_file has 9 dependencies", "Has side effects: network_io, writes_log, modifies_container, modifies_state", "Function add_requested_file complexity analysis: 14 total connections", "Potential issues detected: IndexError, TypeError, KeyError, AttributeError", "Function add_requested_file architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_file_mentions": {"final_confidence": 0.33666666666666667, "total_iterations": 4, "insights": ["Function get_file_mentions has 12 dependencies", "Has side effects: modifies_container, network_io, modifies_state", "Function get_file_mentions complexity analysis: 15 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function get_file_mentions architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.format_chat_chunks": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function format_chat_chunks has 14 dependencies", "Has side effects: modifies_state, modifies_file, network_io, modifies_container", "Function format_chat_chunks complexity analysis: 20 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, AttributeError", "Function format_chat_chunks architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_context_requests": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests complexity analysis: 24 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function process_context_requests architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "commands.cmd_drop": {"final_confidence": 0.38666666666666666, "total_iterations": 4, "insights": ["Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop complexity analysis: 13 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function cmd_drop architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for commands.cmd_drop", "Needs deeper investigation"], "suggested_next_steps": []}, "aider_integration_service.get_symbol_references_between_files": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function get_symbol_references_between_files has 15 dependencies", "Has side effects: modifies_state, modifies_file, network_io, writes_log", "Function get_symbol_references_between_files complexity analysis: 17 total connections", "Potential issues detected: IndexError, PermissionError, TypeError, ImportError, FileNotFoundError, KeyError, ValueError, AttributeError", "Function get_symbol_references_between_files architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.format_chat_chunks": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function format_chat_chunks has 14 dependencies", "Has side effects: modifies_state, modifies_file, network_io, modifies_container", "Function format_chat_chunks complexity analysis: 20 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, AttributeError", "Function format_chat_chunks architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_repo_map": {"final_confidence": 0.38666666666666666, "total_iterations": 4, "insights": ["Function get_repo_map has 7 dependencies", "Has side effects: network_io, database_io, modifies_global, modifies_state", "Function get_repo_map complexity analysis: 17 total connections", "Potential issues detected: TypeError, ImportError, AttributeError", "Function get_repo_map architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_announcements": {"final_confidence": 0.3666666666666667, "total_iterations": 4, "insights": ["Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements complexity analysis: 15 total connections", "Potential issues detected: IndexError, TypeError, KeyError, ZeroDivisionError, AttributeError", "Function get_announcements architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation"], "suggested_next_steps": []}, "context_request_handler.process_context_request": {"final_confidence": 0.3666666666666667, "total_iterations": 4, "insights": ["Function process_context_request has 9 dependencies", "Has side effects: modifies_state, network_io, database_io, writes_log", "Function process_context_request complexity analysis: 19 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function process_context_request architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.__init__": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_file, modifies_state", "Function __init__ complexity analysis: 19 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, ValueError, AttributeError", "Function __init__ architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.__init__", "Needs deeper investigation"], "suggested_next_steps": []}, "io.format_files_for_input": {"final_confidence": 0.4666666666666667, "total_iterations": 4, "insights": ["Function format_files_for_input has 9 dependencies", "Has side effects: modifies_state, network_io, writes_log", "Function format_files_for_input complexity analysis: 11 total connections", "Potential issues detected: KeyError, IndexError, TypeError, ZeroDivisionError, ValueError, AttributeError", "Function format_files_for_input architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for io.format_files_for_input", "Needs deeper investigation"], "suggested_next_steps": []}, "commands.cmd_lint": {"final_confidence": 0.3516666666666667, "total_iterations": 4, "insights": ["Function cmd_lint has 13 dependencies", "Has side effects: modifies_state, network_io, database_io, writes_log", "Function cmd_lint complexity analysis: 14 total connections", "Potential issues detected: IndexError, TypeError, KeyError, ValueError, AttributeError", "Function cmd_lint architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for commands.cmd_lint", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.get_announcements": {"final_confidence": 0.3666666666666667, "total_iterations": 4, "insights": ["Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements complexity analysis: 15 total connections", "Potential issues detected: IndexError, TypeError, KeyError, ZeroDivisionError, AttributeError", "Function get_announcements architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.process_file_requests": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests complexity analysis: 19 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function process_file_requests architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_map_requests": {"final_confidence": 0.33666666666666667, "total_iterations": 4, "insights": ["Function process_map_requests has 12 dependencies", "Has side effects: modifies_state, network_io, writes_log", "Function process_map_requests complexity analysis: 18 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function process_map_requests architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.process_context_requests": {"final_confidence": 0.32666666666666666, "total_iterations": 4, "insights": ["Function process_context_requests has 13 dependencies", "Has side effects: modifies_state, network_io, writes_log", "Function process_context_requests complexity analysis: 22 total connections", "Potential issues detected: I<PERSON>rtError, TypeError, KeyError, AttributeError", "Function process_context_requests architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.get_file_mentions": {"final_confidence": 0.33666666666666667, "total_iterations": 4, "insights": ["Function get_file_mentions has 12 dependencies", "Has side effects: modifies_container, network_io, modifies_state", "Function get_file_mentions complexity analysis: 15 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function get_file_mentions architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation"], "suggested_next_steps": []}, "commands.cmd_add": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function cmd_add has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_file, modifies_state", "Function cmd_add complexity analysis: 16 total connections", "Potential issues detected: KeyError, IndexError, TypeError, ImportError, ZeroDivisionError, AttributeError", "Function cmd_add architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for commands.cmd_add", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.__init__": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_file, modifies_state", "Function __init__ complexity analysis: 19 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function __init__ architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.add_requested_file": {"final_confidence": 0.39166666666666666, "total_iterations": 4, "insights": ["Function add_requested_file has 9 dependencies", "Has side effects: network_io, writes_log, modifies_container, modifies_state", "Function add_requested_file complexity analysis: 14 total connections", "Potential issues detected: IndexError, TypeError, KeyError, AttributeError", "Function add_requested_file architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation"], "suggested_next_steps": []}, "repo.get_tracked_files": {"final_confidence": 0.3566666666666667, "total_iterations": 4, "insights": ["Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_container, modifies_state", "Function get_tracked_files complexity analysis: 15 total connections", "Potential issues detected: IndexError, KeyError, AttributeError", "Function get_tracked_files architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation"], "suggested_next_steps": []}, "debug_search_issue.debug_search_issue": {"final_confidence": 0.31666666666666665, "total_iterations": 4, "insights": ["Function debug_search_issue has 15 dependencies", "Has side effects: modifies_file, network_io, writes_log", "Function debug_search_issue complexity analysis: 15 total connections", "Potential issues detected: ZeroDivisionError, IndexError, PermissionError, TypeError, ImportError, FileNotFoundError, KeyError, ValueError, AttributeError", "Function debug_search_issue architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for debug_search_issue.debug_search_issue", "Needs deeper investigation"], "suggested_next_steps": []}}, "global_insights": ["Function cmd_add has 15 dependencies", "Function get_announcements complexity analysis: 15 total connections", "Potential issues detected: KeyError, IndexError, TypeError, ZeroDivisionError, ValueError, AttributeError", "Function cmd_add complexity analysis: 16 total connections", "Has side effects: network_io, database_io, modifies_global, modifies_state", "Function debug_search_issue has 15 dependencies", "Function process_map_requests complexity analysis: 18 total connections", "Function get_repo_map complexity analysis: 17 total connections", "Function __init__ has 15 dependencies", "Function cmd_drop architectural role: utility", "Function cmd_lint architectural role: utility", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function get_symbol_references_between_files architectural role: utility", "Function process_context_requests architectural role: core", "Function get_tracked_files has 10 dependencies", "Function get_tracked_files complexity analysis: 15 total connections", "Function format_files_for_input complexity analysis: 11 total connections", "Potential issues detected: IndexError, TypeError, KeyError, ZeroDivisionError, AttributeError", "Function process_context_requests complexity analysis: 22 total connections", "Function format_files_for_input has 9 dependencies", "Function process_context_requests complexity analysis: 24 total connections", "Has side effects: modifies_state, modifies_file, network_io, writes_log", "Function get_symbol_references_between_files complexity analysis: 17 total connections", "Potential issues detected: IndexError, KeyError, AttributeError", "High criticality function requiring careful maintenance", "Function get_announcements architectural role: core", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: modifies_container, network_io, modifies_state", "Function get_file_mentions has 12 dependencies", "Function process_context_request has 9 dependencies", "Function get_file_mentions complexity analysis: 15 total connections", "Function get_tracked_files architectural role: core", "Potential issues detected: KeyError, IndexError, TypeError, ImportError, ZeroDivisionError, AttributeError", "Has side effects: network_io, database_io, writes_log, modifies_container, modifies_state", "Function debug_search_issue architectural role: utility", "Function add_requested_file architectural role: core", "Function __init__ architectural role: core", "Function add_requested_file has 9 dependencies", "Function get_announcements has 9 dependencies", "Has side effects: modifies_state, network_io, writes_log", "Has side effects: modifies_state, modifies_file, network_io, modifies_container", "Potential issues detected: IndexError, TypeError, KeyError, ValueError, AttributeError", "Function process_context_request architectural role: core", "Function debug_search_issue complexity analysis: 15 total connections", "Function process_file_requests architectural role: core", "Function get_repo_map architectural role: core", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, ValueError, AttributeError", "Function format_chat_chunks complexity analysis: 20 total connections", "Potential issues detected: TypeError, ImportError, AttributeError", "Function process_context_requests has 15 dependencies", "Function format_chat_chunks architectural role: core", "Has side effects: network_io, database_io, writes_log, modifies_file, modifies_state", "Function process_map_requests architectural role: core", "Has side effects: network_io, writes_log, modifies_container, modifies_state", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, ValueError, AttributeError", "Potential issues detected: IndexError, TypeError, KeyError, AttributeError", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, AttributeError", "Potential issues detected: I<PERSON>rtError, TypeError, KeyError, AttributeError", "Function cmd_lint has 13 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function cmd_tokens complexity analysis: 16 total connections", "Has side effects: network_io, modifies_state", "Function cmd_add architectural role: utility", "Function process_map_requests has 12 dependencies", "Function process_file_requests has 15 dependencies", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function cmd_tokens architectural role: utility", "Potential issues detected: ZeroDivisionError, IndexError, PermissionError, TypeError, ImportError, FileNotFoundError, KeyError, ValueError, AttributeError", "Function cmd_drop has 12 dependencies", "Function cmd_tokens has 15 dependencies", "Function process_file_requests complexity analysis: 19 total connections", "Function cmd_lint complexity analysis: 14 total connections", "Has side effects: modifies_file, network_io, writes_log", "Potential issues detected: IndexError, PermissionError, TypeError, ImportError, FileNotFoundError, KeyError, ValueError, AttributeError", "Function process_context_request complexity analysis: 19 total connections", "Function get_file_mentions architectural role: utility", "Function add_requested_file complexity analysis: 14 total connections", "Has side effects: modifies_state, network_io, database_io, writes_log", "Function process_context_requests has 13 dependencies", "Function format_chat_chunks has 14 dependencies", "Function get_repo_map has 7 dependencies", "Function cmd_drop complexity analysis: 13 total connections", "Function __init__ complexity analysis: 19 total connections", "Function format_files_for_input architectural role: utility"], "analysis_summary": {"total_entities_analyzed": 26, "total_iterations": 4, "low_confidence_count": 26, "high_confidence_count": 0, "global_insights_count": 85, "last_analysis": "2025-05-28T23:09:53.102580+00:00"}, "confidence_summary": {"average_confidence": 0.34762820512820514, "min_confidence": 0.31666666666666665, "max_confidence": 0.4666666666666667, "low_confidence_count": 26, "total_entities": 26}, "iteration_history": [{"iteration": 1, "timestamp": "2025-05-28T23:09:52.817360+00:00", "entities_count": 26, "confidence": 0.3176282051282051, "status": "continue"}, {"iteration": 2, "timestamp": "2025-05-28T23:09:52.900764+00:00", "entities_count": 26, "confidence": 0.3176282051282051, "status": "continue"}, {"iteration": 3, "timestamp": "2025-05-28T23:09:52.982564+00:00", "entities_count": 26, "confidence": 0.34762820512820514, "status": "continue"}, {"iteration": 4, "timestamp": "2025-05-28T23:09:53.102580+00:00", "entities_count": 26, "confidence": 0.34762820512820514, "status": "continue"}], "analysis_metadata": {"project_path": "C:\\Users\\<USER>\\Documents\\aider_project\\aider__500", "max_tokens": 4000, "engine_version": "IAA Protocol v1.0", "analysis_type": "multi_turn_reasoning"}}, "Feature Development": {"task": "Analyze the architecture for adding real-time collaboration features", "task_type": "feature_development", "total_iterations": 3, "overall_confidence": 0.35012820512820514, "entity_summaries": {"base_coder.get_repo_map": {"final_confidence": 0.38666666666666666, "total_iterations": 7, "insights": ["Function get_repo_map has 7 dependencies", "Has side effects: network_io, database_io, modifies_global, modifies_state", "Function get_repo_map complexity analysis: 17 total connections", "Potential issues detected: TypeError, ImportError, AttributeError", "Function get_repo_map architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation"], "suggested_next_steps": []}, "commands.cmd_tokens": {"final_confidence": 0.31666666666666665, "total_iterations": 7, "insights": ["Function cmd_tokens has 15 dependencies", "Has side effects: modifies_state, modifies_file, network_io, writes_log", "Function cmd_tokens complexity analysis: 16 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, ValueError, AttributeError", "Function cmd_tokens architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for commands.cmd_tokens", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_file_requests": {"final_confidence": 0.31666666666666665, "total_iterations": 7, "insights": ["Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests complexity analysis: 19 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function process_file_requests architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.add_requested_file": {"final_confidence": 0.39166666666666666, "total_iterations": 7, "insights": ["Function add_requested_file has 9 dependencies", "Has side effects: network_io, writes_log, modifies_container, modifies_state", "Function add_requested_file complexity analysis: 14 total connections", "Potential issues detected: IndexError, TypeError, KeyError, AttributeError", "Function add_requested_file architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_file_mentions": {"final_confidence": 0.33666666666666667, "total_iterations": 7, "insights": ["Function get_file_mentions has 12 dependencies", "Has side effects: modifies_container, network_io, modifies_state", "Function get_file_mentions complexity analysis: 15 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function get_file_mentions architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.format_chat_chunks": {"final_confidence": 0.31666666666666665, "total_iterations": 7, "insights": ["Function format_chat_chunks has 14 dependencies", "Has side effects: modifies_state, modifies_file, network_io, modifies_container", "Function format_chat_chunks complexity analysis: 20 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, AttributeError", "Function format_chat_chunks architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_context_requests": {"final_confidence": 0.31666666666666665, "total_iterations": 7, "insights": ["Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests complexity analysis: 24 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function process_context_requests architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "commands.cmd_drop": {"final_confidence": 0.38666666666666666, "total_iterations": 7, "insights": ["Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop complexity analysis: 13 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function cmd_drop architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for commands.cmd_drop", "Needs deeper investigation"], "suggested_next_steps": []}, "aider_integration_service.get_symbol_references_between_files": {"final_confidence": 0.31666666666666665, "total_iterations": 7, "insights": ["Function get_symbol_references_between_files has 15 dependencies", "Has side effects: modifies_state, modifies_file, network_io, writes_log", "Function get_symbol_references_between_files complexity analysis: 17 total connections", "Potential issues detected: IndexError, PermissionError, TypeError, ImportError, FileNotFoundError, KeyError, ValueError, AttributeError", "Function get_symbol_references_between_files architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.format_chat_chunks": {"final_confidence": 0.31666666666666665, "total_iterations": 7, "insights": ["Function format_chat_chunks has 14 dependencies", "Has side effects: modifies_state, modifies_file, network_io, modifies_container", "Function format_chat_chunks complexity analysis: 20 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, AttributeError", "Function format_chat_chunks architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_repo_map": {"final_confidence": 0.38666666666666666, "total_iterations": 7, "insights": ["Function get_repo_map has 7 dependencies", "Has side effects: network_io, database_io, modifies_global, modifies_state", "Function get_repo_map complexity analysis: 17 total connections", "Potential issues detected: TypeError, ImportError, AttributeError", "Function get_repo_map architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_announcements": {"final_confidence": 0.3666666666666667, "total_iterations": 7, "insights": ["Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements complexity analysis: 15 total connections", "Potential issues detected: IndexError, TypeError, KeyError, ZeroDivisionError, AttributeError", "Function get_announcements architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation"], "suggested_next_steps": []}, "context_request_handler.process_context_request": {"final_confidence": 0.3666666666666667, "total_iterations": 7, "insights": ["Function process_context_request has 9 dependencies", "Has side effects: modifies_state, network_io, database_io, writes_log", "Function process_context_request complexity analysis: 19 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function process_context_request architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation"], "suggested_next_steps": []}, "io.format_files_for_input": {"final_confidence": 0.4666666666666667, "total_iterations": 7, "insights": ["Function format_files_for_input has 9 dependencies", "Has side effects: modifies_state, network_io, writes_log", "Function format_files_for_input complexity analysis: 11 total connections", "Potential issues detected: KeyError, IndexError, TypeError, ZeroDivisionError, ValueError, AttributeError", "Function format_files_for_input architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for io.format_files_for_input", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.__init__": {"final_confidence": 0.31666666666666665, "total_iterations": 7, "insights": ["Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_file, modifies_state", "Function __init__ complexity analysis: 19 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, ValueError, AttributeError", "Function __init__ architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.__init__", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.get_announcements": {"final_confidence": 0.3666666666666667, "total_iterations": 7, "insights": ["Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements complexity analysis: 15 total connections", "Potential issues detected: IndexError, TypeError, KeyError, ZeroDivisionError, AttributeError", "Function get_announcements architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_map_requests": {"final_confidence": 0.33666666666666667, "total_iterations": 7, "insights": ["Function process_map_requests has 12 dependencies", "Has side effects: modifies_state, network_io, writes_log", "Function process_map_requests complexity analysis: 18 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function process_map_requests architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.process_file_requests": {"final_confidence": 0.31666666666666665, "total_iterations": 7, "insights": ["Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests complexity analysis: 19 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function process_file_requests architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.process_context_requests": {"final_confidence": 0.32666666666666666, "total_iterations": 7, "insights": ["Function process_context_requests has 13 dependencies", "Has side effects: modifies_state, network_io, writes_log", "Function process_context_requests complexity analysis: 22 total connections", "Potential issues detected: I<PERSON>rtError, TypeError, KeyError, AttributeError", "Function process_context_requests architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "mdstream.update": {"final_confidence": 0.3666666666666667, "total_iterations": 3, "insights": ["Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update complexity analysis: 19 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, ValueError, AttributeError", "Function update architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for mdstream.update", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.get_file_mentions": {"final_confidence": 0.33666666666666667, "total_iterations": 7, "insights": ["Function get_file_mentions has 12 dependencies", "Has side effects: modifies_container, network_io, modifies_state", "Function get_file_mentions complexity analysis: 15 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function get_file_mentions architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.__init__": {"final_confidence": 0.31666666666666665, "total_iterations": 7, "insights": ["Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_file, modifies_state", "Function __init__ complexity analysis: 19 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function __init__ architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation"], "suggested_next_steps": []}, "commands.cmd_add": {"final_confidence": 0.31666666666666665, "total_iterations": 7, "insights": ["Function cmd_add has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_file, modifies_state", "Function cmd_add complexity analysis: 16 total connections", "Potential issues detected: KeyError, IndexError, TypeError, ImportError, ZeroDivisionError, AttributeError", "Function cmd_add architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for commands.cmd_add", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.add_requested_file": {"final_confidence": 0.39166666666666666, "total_iterations": 7, "insights": ["Function add_requested_file has 9 dependencies", "Has side effects: network_io, writes_log, modifies_container, modifies_state", "Function add_requested_file complexity analysis: 14 total connections", "Potential issues detected: IndexError, TypeError, KeyError, AttributeError", "Function add_requested_file architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation"], "suggested_next_steps": []}, "repomap.get_repo_overview": {"final_confidence": 0.3666666666666667, "total_iterations": 3, "insights": ["Function get_repo_overview has 9 dependencies", "Has side effects: modifies_state, modifies_container, network_io, writes_log", "Function get_repo_overview complexity analysis: 17 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, AttributeError", "Function get_repo_overview architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation"], "suggested_next_steps": []}, "repo.get_tracked_files": {"final_confidence": 0.3566666666666667, "total_iterations": 7, "insights": ["Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_container, modifies_state", "Function get_tracked_files complexity analysis: 15 total connections", "Potential issues detected: IndexError, KeyError, AttributeError", "Function get_tracked_files architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation"], "suggested_next_steps": []}}, "global_insights": ["Function cmd_add has 15 dependencies", "Function get_announcements complexity analysis: 15 total connections", "Potential issues detected: KeyError, IndexError, TypeError, ZeroDivisionError, ValueError, AttributeError", "Function cmd_add complexity analysis: 16 total connections", "Has side effects: network_io, database_io, modifies_global, modifies_state", "Function process_map_requests complexity analysis: 18 total connections", "Has side effects: modifies_state, modifies_container, network_io, writes_log", "Function get_repo_map complexity analysis: 17 total connections", "Function __init__ has 15 dependencies", "Function cmd_drop architectural role: utility", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function get_symbol_references_between_files architectural role: utility", "Function process_context_requests architectural role: core", "Function get_tracked_files has 10 dependencies", "Function get_tracked_files complexity analysis: 15 total connections", "Function format_files_for_input complexity analysis: 11 total connections", "Potential issues detected: IndexError, TypeError, KeyError, ZeroDivisionError, AttributeError", "Function process_context_requests complexity analysis: 22 total connections", "Function format_files_for_input has 9 dependencies", "Function process_context_requests complexity analysis: 24 total connections", "Has side effects: modifies_state, modifies_file, network_io, writes_log", "Function get_symbol_references_between_files complexity analysis: 17 total connections", "Potential issues detected: IndexError, KeyError, AttributeError", "High criticality function requiring careful maintenance", "Function get_announcements architectural role: core", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: modifies_container, network_io, modifies_state", "Function get_file_mentions has 12 dependencies", "Function process_context_request has 9 dependencies", "Function get_file_mentions complexity analysis: 15 total connections", "Function get_tracked_files architectural role: core", "Function update architectural role: core", "Potential issues detected: KeyError, IndexError, TypeError, ImportError, ZeroDivisionError, AttributeError", "Has side effects: network_io, database_io, writes_log, modifies_container, modifies_state", "Function add_requested_file architectural role: core", "Function __init__ architectural role: core", "Function add_requested_file has 9 dependencies", "Function get_announcements has 9 dependencies", "Has side effects: modifies_state, network_io, writes_log", "Has side effects: modifies_state, modifies_file, network_io, modifies_container", "Function process_context_request architectural role: core", "Function process_file_requests architectural role: core", "Function get_repo_map architectural role: core", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, ValueError, AttributeError", "Function format_chat_chunks complexity analysis: 20 total connections", "Potential issues detected: TypeError, ImportError, AttributeError", "Function process_context_requests has 15 dependencies", "Function format_chat_chunks architectural role: core", "Has side effects: network_io, database_io, writes_log, modifies_file, modifies_state", "Function process_map_requests architectural role: core", "Has side effects: network_io, writes_log, modifies_container, modifies_state", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, ValueError, AttributeError", "Potential issues detected: IndexError, TypeError, KeyError, AttributeError", "Function get_repo_overview has 9 dependencies", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, AttributeError", "Potential issues detected: I<PERSON>rtError, TypeError, KeyError, AttributeError", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function cmd_tokens complexity analysis: 16 total connections", "Has side effects: network_io, modifies_state", "Function cmd_add architectural role: utility", "Function get_repo_overview architectural role: core", "Function process_map_requests has 12 dependencies", "Function process_file_requests has 15 dependencies", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function cmd_tokens architectural role: utility", "Function cmd_drop has 12 dependencies", "Function cmd_tokens has 15 dependencies", "Function process_file_requests complexity analysis: 19 total connections", "Function update complexity analysis: 19 total connections", "Potential issues detected: IndexError, PermissionError, TypeError, ImportError, FileNotFoundError, KeyError, ValueError, AttributeError", "Function process_context_request complexity analysis: 19 total connections", "Function get_file_mentions architectural role: utility", "Function add_requested_file complexity analysis: 14 total connections", "Has side effects: modifies_state, network_io, database_io, writes_log", "Function process_context_requests has 13 dependencies", "Function format_chat_chunks has 14 dependencies", "Function update has 9 dependencies", "Function get_repo_overview complexity analysis: 17 total connections", "Function get_repo_map has 7 dependencies", "Function cmd_drop complexity analysis: 13 total connections", "Function __init__ complexity analysis: 19 total connections", "Function format_files_for_input architectural role: utility"], "analysis_summary": {"total_entities_analyzed": 28, "total_iterations": 7, "low_confidence_count": 28, "high_confidence_count": 0, "global_insights_count": 92, "last_analysis": "2025-05-28T23:09:53.366294+00:00"}, "confidence_summary": {"average_confidence": 0.3489880952380952, "min_confidence": 0.31666666666666665, "max_confidence": 0.4666666666666667, "low_confidence_count": 28, "total_entities": 28}, "iteration_history": [{"iteration": 1, "timestamp": "2025-05-28T23:09:53.193851+00:00", "entities_count": 26, "confidence": 0.32012820512820517, "status": "continue"}, {"iteration": 2, "timestamp": "2025-05-28T23:09:53.280418+00:00", "entities_count": 26, "confidence": 0.32012820512820517, "status": "continue"}, {"iteration": 3, "timestamp": "2025-05-28T23:09:53.366294+00:00", "entities_count": 26, "confidence": 0.35012820512820514, "status": "continue"}], "analysis_metadata": {"project_path": "C:\\Users\\<USER>\\Documents\\aider_project\\aider__500", "max_tokens": 4000, "engine_version": "IAA Protocol v1.0", "analysis_type": "multi_turn_reasoning"}}, "Code Refactoring": {"task": "Identify opportunities to refactor the context selection logic for better modularity", "task_type": "refactoring", "total_iterations": 3, "overall_confidence": 0.3478205128205128, "entity_summaries": {"base_coder.get_repo_map": {"final_confidence": 0.38666666666666666, "total_iterations": 10, "insights": ["Function get_repo_map has 7 dependencies", "Has side effects: network_io, database_io, modifies_global, modifies_state", "Function get_repo_map complexity analysis: 17 total connections", "Potential issues detected: TypeError, ImportError, AttributeError", "Function get_repo_map architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation"], "suggested_next_steps": []}, "commands.cmd_tokens": {"final_confidence": 0.31666666666666665, "total_iterations": 10, "insights": ["Function cmd_tokens has 15 dependencies", "Has side effects: modifies_state, modifies_file, network_io, writes_log", "Function cmd_tokens complexity analysis: 16 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, ValueError, AttributeError", "Function cmd_tokens architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for commands.cmd_tokens", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_file_requests": {"final_confidence": 0.31666666666666665, "total_iterations": 10, "insights": ["Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests complexity analysis: 19 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function process_file_requests architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.add_requested_file": {"final_confidence": 0.39166666666666666, "total_iterations": 10, "insights": ["Function add_requested_file has 9 dependencies", "Has side effects: network_io, writes_log, modifies_container, modifies_state", "Function add_requested_file complexity analysis: 14 total connections", "Potential issues detected: IndexError, TypeError, KeyError, AttributeError", "Function add_requested_file architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_file_mentions": {"final_confidence": 0.33666666666666667, "total_iterations": 10, "insights": ["Function get_file_mentions has 12 dependencies", "Has side effects: modifies_container, network_io, modifies_state", "Function get_file_mentions complexity analysis: 15 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function get_file_mentions architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.format_chat_chunks": {"final_confidence": 0.31666666666666665, "total_iterations": 10, "insights": ["Function format_chat_chunks has 14 dependencies", "Has side effects: modifies_state, modifies_file, network_io, modifies_container", "Function format_chat_chunks complexity analysis: 20 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, AttributeError", "Function format_chat_chunks architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_context_requests": {"final_confidence": 0.31666666666666665, "total_iterations": 10, "insights": ["Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests complexity analysis: 24 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function process_context_requests architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "commands.cmd_drop": {"final_confidence": 0.38666666666666666, "total_iterations": 10, "insights": ["Function cmd_drop has 12 dependencies", "Has side effects: network_io, modifies_state", "Function cmd_drop complexity analysis: 13 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function cmd_drop architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for commands.cmd_drop", "Needs deeper investigation"], "suggested_next_steps": []}, "aider_integration_service.get_symbol_references_between_files": {"final_confidence": 0.31666666666666665, "total_iterations": 10, "insights": ["Function get_symbol_references_between_files has 15 dependencies", "Has side effects: modifies_state, modifies_file, network_io, writes_log", "Function get_symbol_references_between_files complexity analysis: 17 total connections", "Potential issues detected: IndexError, PermissionError, TypeError, ImportError, FileNotFoundError, KeyError, ValueError, AttributeError", "Function get_symbol_references_between_files architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.format_chat_chunks": {"final_confidence": 0.31666666666666665, "total_iterations": 10, "insights": ["Function format_chat_chunks has 14 dependencies", "Has side effects: modifies_state, modifies_file, network_io, modifies_container", "Function format_chat_chunks complexity analysis: 20 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, AttributeError", "Function format_chat_chunks architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_announcements": {"final_confidence": 0.3666666666666667, "total_iterations": 10, "insights": ["Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements complexity analysis: 15 total connections", "Potential issues detected: IndexError, TypeError, KeyError, ZeroDivisionError, AttributeError", "Function get_announcements architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation"], "suggested_next_steps": []}, "context_request_handler.process_context_request": {"final_confidence": 0.3666666666666667, "total_iterations": 10, "insights": ["Function process_context_request has 9 dependencies", "Has side effects: modifies_state, network_io, database_io, writes_log", "Function process_context_request complexity analysis: 19 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function process_context_request architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation"], "suggested_next_steps": []}, "context_request_handler.parse_context_request": {"final_confidence": 0.32666666666666666, "total_iterations": 3, "insights": ["Function parse_context_request has 13 dependencies", "Has side effects: network_io, writes_log", "Function parse_context_request complexity analysis: 19 total connections", "Potential issues detected: IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function parse_context_request architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for context_request_handler.parse_context_request", "Needs deeper investigation"], "suggested_next_steps": []}, "io.format_files_for_input": {"final_confidence": 0.4666666666666667, "total_iterations": 10, "insights": ["Function format_files_for_input has 9 dependencies", "Has side effects: modifies_state, network_io, writes_log", "Function format_files_for_input complexity analysis: 11 total connections", "Potential issues detected: KeyError, IndexError, TypeError, ZeroDivisionError, ValueError, AttributeError", "Function format_files_for_input architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for io.format_files_for_input", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.__init__": {"final_confidence": 0.31666666666666665, "total_iterations": 10, "insights": ["Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_file, modifies_state", "Function __init__ complexity analysis: 19 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, ValueError, AttributeError", "Function __init__ architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.__init__", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.get_announcements": {"final_confidence": 0.3666666666666667, "total_iterations": 10, "insights": ["Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements complexity analysis: 15 total connections", "Potential issues detected: IndexError, TypeError, KeyError, ZeroDivisionError, AttributeError", "Function get_announcements architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_map_requests": {"final_confidence": 0.33666666666666667, "total_iterations": 10, "insights": ["Function process_map_requests has 12 dependencies", "Has side effects: modifies_state, network_io, writes_log", "Function process_map_requests complexity analysis: 18 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function process_map_requests architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.process_file_requests": {"final_confidence": 0.31666666666666665, "total_iterations": 10, "insights": ["Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests complexity analysis: 19 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function process_file_requests architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.process_context_requests": {"final_confidence": 0.32666666666666666, "total_iterations": 10, "insights": ["Function process_context_requests has 13 dependencies", "Has side effects: modifies_state, network_io, writes_log", "Function process_context_requests complexity analysis: 22 total connections", "Potential issues detected: I<PERSON>rtError, TypeError, KeyError, AttributeError", "Function process_context_requests architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "mdstream.update": {"final_confidence": 0.3666666666666667, "total_iterations": 6, "insights": ["Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update complexity analysis: 19 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, ValueError, AttributeError", "Function update architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for mdstream.update", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.get_file_mentions": {"final_confidence": 0.33666666666666667, "total_iterations": 10, "insights": ["Function get_file_mentions has 12 dependencies", "Has side effects: modifies_container, network_io, modifies_state", "Function get_file_mentions complexity analysis: 15 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function get_file_mentions architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.__init__": {"final_confidence": 0.31666666666666665, "total_iterations": 10, "insights": ["Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_file, modifies_state", "Function __init__ complexity analysis: 19 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function __init__ architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation"], "suggested_next_steps": []}, "commands.cmd_add": {"final_confidence": 0.31666666666666665, "total_iterations": 10, "insights": ["Function cmd_add has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_file, modifies_state", "Function cmd_add complexity analysis: 16 total connections", "Potential issues detected: KeyError, IndexError, TypeError, ImportError, ZeroDivisionError, AttributeError", "Function cmd_add architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for commands.cmd_add", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.add_requested_file": {"final_confidence": 0.39166666666666666, "total_iterations": 10, "insights": ["Function add_requested_file has 9 dependencies", "Has side effects: network_io, writes_log, modifies_container, modifies_state", "Function add_requested_file complexity analysis: 14 total connections", "Potential issues detected: IndexError, TypeError, KeyError, AttributeError", "Function add_requested_file architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation"], "suggested_next_steps": []}, "repomap.get_repo_overview": {"final_confidence": 0.3666666666666667, "total_iterations": 6, "insights": ["Function get_repo_overview has 9 dependencies", "Has side effects: modifies_state, modifies_container, network_io, writes_log", "Function get_repo_overview complexity analysis: 17 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, AttributeError", "Function get_repo_overview architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation"], "suggested_next_steps": []}, "repo.get_tracked_files": {"final_confidence": 0.3566666666666667, "total_iterations": 10, "insights": ["Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_container, modifies_state", "Function get_tracked_files complexity analysis: 15 total connections", "Potential issues detected: IndexError, KeyError, AttributeError", "Function get_tracked_files architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation"], "suggested_next_steps": []}}, "global_insights": ["Function cmd_add has 15 dependencies", "Function get_announcements complexity analysis: 15 total connections", "Potential issues detected: KeyError, IndexError, TypeError, ZeroDivisionError, ValueError, AttributeError", "Function cmd_add complexity analysis: 16 total connections", "Has side effects: network_io, database_io, modifies_global, modifies_state", "Function process_map_requests complexity analysis: 18 total connections", "Has side effects: modifies_state, modifies_container, network_io, writes_log", "Function get_repo_map complexity analysis: 17 total connections", "Function __init__ has 15 dependencies", "Function cmd_drop architectural role: utility", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function get_symbol_references_between_files architectural role: utility", "Function process_context_requests architectural role: core", "Function get_tracked_files has 10 dependencies", "Function get_tracked_files complexity analysis: 15 total connections", "Function format_files_for_input complexity analysis: 11 total connections", "Potential issues detected: IndexError, TypeError, KeyError, ZeroDivisionError, AttributeError", "Function process_context_requests complexity analysis: 22 total connections", "Function format_files_for_input has 9 dependencies", "Function process_context_requests complexity analysis: 24 total connections", "Has side effects: modifies_state, modifies_file, network_io, writes_log", "Function get_symbol_references_between_files complexity analysis: 17 total connections", "Function parse_context_request architectural role: core", "Potential issues detected: IndexError, KeyError, AttributeError", "High criticality function requiring careful maintenance", "Function get_announcements architectural role: core", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function get_symbol_references_between_files has 15 dependencies", "Has side effects: modifies_container, network_io, modifies_state", "Function process_context_request has 9 dependencies", "Function get_file_mentions has 12 dependencies", "Function get_file_mentions complexity analysis: 15 total connections", "Function get_tracked_files architectural role: core", "Function update architectural role: core", "Potential issues detected: KeyError, IndexError, TypeError, ImportError, ZeroDivisionError, AttributeError", "Has side effects: network_io, database_io, writes_log, modifies_container, modifies_state", "Potential issues detected: IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function add_requested_file architectural role: core", "Function __init__ architectural role: core", "Function add_requested_file has 9 dependencies", "Function get_announcements has 9 dependencies", "Function parse_context_request has 13 dependencies", "Has side effects: modifies_state, network_io, writes_log", "Has side effects: modifies_state, modifies_file, network_io, modifies_container", "Function process_context_request architectural role: core", "Function process_file_requests architectural role: core", "Function get_repo_map architectural role: core", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, ValueError, AttributeError", "Function format_chat_chunks complexity analysis: 20 total connections", "Potential issues detected: TypeError, ImportError, AttributeError", "Function process_context_requests has 15 dependencies", "Function parse_context_request complexity analysis: 19 total connections", "Function format_chat_chunks architectural role: core", "Has side effects: network_io, database_io, writes_log, modifies_file, modifies_state", "Function process_map_requests architectural role: core", "Has side effects: network_io, writes_log, modifies_container, modifies_state", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, ValueError, AttributeError", "Potential issues detected: IndexError, TypeError, KeyError, AttributeError", "Function get_repo_overview has 9 dependencies", "Has side effects: network_io, writes_log", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, AttributeError", "Potential issues detected: I<PERSON>rtError, TypeError, KeyError, AttributeError", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function cmd_tokens complexity analysis: 16 total connections", "Has side effects: network_io, modifies_state", "Function cmd_add architectural role: utility", "Function get_repo_overview architectural role: core", "Function process_map_requests has 12 dependencies", "Function process_file_requests has 15 dependencies", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function cmd_tokens architectural role: utility", "Function cmd_drop has 12 dependencies", "Function cmd_tokens has 15 dependencies", "Function process_file_requests complexity analysis: 19 total connections", "Function update complexity analysis: 19 total connections", "Potential issues detected: IndexError, PermissionError, TypeError, ImportError, FileNotFoundError, KeyError, ValueError, AttributeError", "Function process_context_request complexity analysis: 19 total connections", "Function get_file_mentions architectural role: utility", "Function add_requested_file complexity analysis: 14 total connections", "Has side effects: modifies_state, network_io, database_io, writes_log", "Function process_context_requests has 13 dependencies", "Function format_chat_chunks has 14 dependencies", "Function update has 9 dependencies", "Function get_repo_overview complexity analysis: 17 total connections", "Function get_repo_map has 7 dependencies", "Function cmd_drop complexity analysis: 13 total connections", "Function __init__ complexity analysis: 19 total connections", "Function format_files_for_input architectural role: utility"], "analysis_summary": {"total_entities_analyzed": 29, "total_iterations": 10, "low_confidence_count": 29, "high_confidence_count": 0, "global_insights_count": 97, "last_analysis": "2025-05-28T23:09:53.665944+00:00"}, "confidence_summary": {"average_confidence": 0.34821839080459766, "min_confidence": 0.31666666666666665, "max_confidence": 0.4666666666666667, "low_confidence_count": 29, "total_entities": 29}, "iteration_history": [{"iteration": 1, "timestamp": "2025-05-28T23:09:53.454293+00:00", "entities_count": 26, "confidence": 0.3178205128205128, "status": "continue"}, {"iteration": 2, "timestamp": "2025-05-28T23:09:53.542368+00:00", "entities_count": 26, "confidence": 0.3178205128205128, "status": "continue"}, {"iteration": 3, "timestamp": "2025-05-28T23:09:53.665944+00:00", "entities_count": 26, "confidence": 0.3478205128205128, "status": "continue"}], "analysis_metadata": {"project_path": "C:\\Users\\<USER>\\Documents\\aider_project\\aider__500", "max_tokens": 4000, "engine_version": "IAA Protocol v1.0", "analysis_type": "multi_turn_reasoning"}}, "Documentation Analysis": {"task": "Assess documentation coverage and identify areas needing better documentation", "task_type": "documentation", "total_iterations": 2, "overall_confidence": 0.3178205128205128, "entity_summaries": {"base_coder.get_repo_map": {"final_confidence": 0.3566666666666667, "total_iterations": 12, "insights": ["Function get_repo_map has 7 dependencies", "Has side effects: network_io, database_io, modifies_global, modifies_state", "Function get_repo_map complexity analysis: 17 total connections", "Potential issues detected: TypeError, ImportError, AttributeError", "Function get_repo_map architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.get_repo_map", "Needs deeper investigation"], "suggested_next_steps": []}, "commands.cmd_tokens": {"final_confidence": 0.2866666666666667, "total_iterations": 12, "insights": ["Function cmd_tokens has 15 dependencies", "Has side effects: modifies_state, modifies_file, network_io, writes_log", "Function cmd_tokens complexity analysis: 16 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, ValueError, AttributeError", "Function cmd_tokens architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for commands.cmd_tokens", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_file_requests": {"final_confidence": 0.2866666666666667, "total_iterations": 12, "insights": ["Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests complexity analysis: 19 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function process_file_requests architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.process_file_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.add_requested_file": {"final_confidence": 0.3616666666666667, "total_iterations": 12, "insights": ["Function add_requested_file has 9 dependencies", "Has side effects: network_io, writes_log, modifies_container, modifies_state", "Function add_requested_file complexity analysis: 14 total connections", "Potential issues detected: IndexError, TypeError, KeyError, AttributeError", "Function add_requested_file architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.add_requested_file", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_file_mentions": {"final_confidence": 0.3066666666666667, "total_iterations": 12, "insights": ["Function get_file_mentions has 12 dependencies", "Has side effects: modifies_container, network_io, modifies_state", "Function get_file_mentions complexity analysis: 15 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function get_file_mentions architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.get_file_mentions", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.format_chat_chunks": {"final_confidence": 0.2866666666666667, "total_iterations": 12, "insights": ["Function format_chat_chunks has 14 dependencies", "Has side effects: modifies_state, modifies_file, network_io, modifies_container", "Function format_chat_chunks complexity analysis: 20 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, AttributeError", "Function format_chat_chunks architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.format_chat_chunks", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_context_requests": {"final_confidence": 0.2866666666666667, "total_iterations": 12, "insights": ["Function process_context_requests has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function process_context_requests complexity analysis: 24 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function process_context_requests architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.process_context_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "aider_integration_service.get_symbol_references_between_files": {"final_confidence": 0.2866666666666667, "total_iterations": 12, "insights": ["Function get_symbol_references_between_files has 15 dependencies", "Has side effects: modifies_state, modifies_file, network_io, writes_log", "Function get_symbol_references_between_files complexity analysis: 17 total connections", "Potential issues detected: IndexError, PermissionError, TypeError, ImportError, FileNotFoundError, KeyError, ValueError, AttributeError", "Function get_symbol_references_between_files architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for aider_integration_service.get_symbol_references_between_files", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.format_chat_chunks": {"final_confidence": 0.2866666666666667, "total_iterations": 12, "insights": ["Function format_chat_chunks has 14 dependencies", "Has side effects: modifies_state, modifies_file, network_io, modifies_container", "Function format_chat_chunks complexity analysis: 20 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, AttributeError", "Function format_chat_chunks architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.format_chat_chunks", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_repo_map": {"final_confidence": 0.3566666666666667, "total_iterations": 9, "insights": ["Function get_repo_map has 7 dependencies", "Has side effects: network_io, database_io, modifies_global, modifies_state", "Function get_repo_map complexity analysis: 17 total connections", "Potential issues detected: TypeError, ImportError, AttributeError", "Function get_repo_map architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.get_repo_map", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.get_announcements": {"final_confidence": 0.33666666666666667, "total_iterations": 12, "insights": ["Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements complexity analysis: 15 total connections", "Potential issues detected: IndexError, TypeError, KeyError, ZeroDivisionError, AttributeError", "Function get_announcements architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.get_announcements", "Needs deeper investigation"], "suggested_next_steps": []}, "context_request_handler.process_context_request": {"final_confidence": 0.33666666666666667, "total_iterations": 12, "insights": ["Function process_context_request has 9 dependencies", "Has side effects: modifies_state, network_io, database_io, writes_log", "Function process_context_request complexity analysis: 19 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function process_context_request architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for context_request_handler.process_context_request", "Needs deeper investigation"], "suggested_next_steps": []}, "context_request_handler.parse_context_request": {"final_confidence": 0.29666666666666663, "total_iterations": 5, "insights": ["Function parse_context_request has 13 dependencies", "Has side effects: network_io, writes_log", "Function parse_context_request complexity analysis: 19 total connections", "Potential issues detected: IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function parse_context_request architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for context_request_handler.parse_context_request", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.__init__": {"final_confidence": 0.2866666666666667, "total_iterations": 12, "insights": ["Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_file, modifies_state", "Function __init__ complexity analysis: 19 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, ValueError, AttributeError", "Function __init__ architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.__init__", "Needs deeper investigation"], "suggested_next_steps": []}, "io.format_files_for_input": {"final_confidence": 0.4366666666666667, "total_iterations": 12, "insights": ["Function format_files_for_input has 9 dependencies", "Has side effects: modifies_state, network_io, writes_log", "Function format_files_for_input complexity analysis: 11 total connections", "Potential issues detected: KeyError, IndexError, TypeError, ZeroDivisionError, ValueError, AttributeError", "Function format_files_for_input architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for io.format_files_for_input", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.get_announcements": {"final_confidence": 0.33666666666666667, "total_iterations": 12, "insights": ["Function get_announcements has 9 dependencies", "Has side effects: network_io, modifies_state", "Function get_announcements complexity analysis: 15 total connections", "Potential issues detected: IndexError, TypeError, KeyError, ZeroDivisionError, AttributeError", "Function get_announcements architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.get_announcements", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.process_map_requests": {"final_confidence": 0.3066666666666667, "total_iterations": 12, "insights": ["Function process_map_requests has 12 dependencies", "Has side effects: modifies_state, network_io, writes_log", "Function process_map_requests complexity analysis: 18 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function process_map_requests architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.process_map_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.process_file_requests": {"final_confidence": 0.2866666666666667, "total_iterations": 12, "insights": ["Function process_file_requests has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests complexity analysis: 19 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function process_file_requests architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.process_file_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.process_context_requests": {"final_confidence": 0.29666666666666663, "total_iterations": 12, "insights": ["Function process_context_requests has 13 dependencies", "Has side effects: modifies_state, network_io, writes_log", "Function process_context_requests complexity analysis: 22 total connections", "Potential issues detected: I<PERSON>rtError, TypeError, KeyError, AttributeError", "Function process_context_requests architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.process_context_requests", "Needs deeper investigation"], "suggested_next_steps": []}, "mdstream.update": {"final_confidence": 0.33666666666666667, "total_iterations": 8, "insights": ["Function update has 9 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function update complexity analysis: 19 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, ValueError, AttributeError", "Function update architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for mdstream.update", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder.get_file_mentions": {"final_confidence": 0.3066666666666667, "total_iterations": 12, "insights": ["Function get_file_mentions has 12 dependencies", "Has side effects: modifies_container, network_io, modifies_state", "Function get_file_mentions complexity analysis: 15 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function get_file_mentions architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder.get_file_mentions", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.__init__": {"final_confidence": 0.2866666666666667, "total_iterations": 12, "insights": ["Function __init__ has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_file, modifies_state", "Function __init__ complexity analysis: 19 total connections", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Function __init__ architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.__init__", "Needs deeper investigation"], "suggested_next_steps": []}, "commands.cmd_add": {"final_confidence": 0.2866666666666667, "total_iterations": 12, "insights": ["Function cmd_add has 15 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_file, modifies_state", "Function cmd_add complexity analysis: 16 total connections", "Potential issues detected: KeyError, IndexError, TypeError, ImportError, ZeroDivisionError, AttributeError", "Function cmd_add architectural role: utility", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for commands.cmd_add", "Needs deeper investigation"], "suggested_next_steps": []}, "base_coder_old.add_requested_file": {"final_confidence": 0.3616666666666667, "total_iterations": 12, "insights": ["Function add_requested_file has 9 dependencies", "Has side effects: network_io, writes_log, modifies_container, modifies_state", "Function add_requested_file complexity analysis: 14 total connections", "Potential issues detected: IndexError, TypeError, KeyError, AttributeError", "Function add_requested_file architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for base_coder_old.add_requested_file", "Needs deeper investigation"], "suggested_next_steps": []}, "repomap.get_repo_overview": {"final_confidence": 0.33666666666666667, "total_iterations": 8, "insights": ["Function get_repo_overview has 9 dependencies", "Has side effects: modifies_state, modifies_container, network_io, writes_log", "Function get_repo_overview complexity analysis: 17 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, AttributeError", "Function get_repo_overview architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for repomap.get_repo_overview", "Needs deeper investigation"], "suggested_next_steps": []}, "repo.get_tracked_files": {"final_confidence": 0.32666666666666666, "total_iterations": 12, "insights": ["Function get_tracked_files has 10 dependencies", "Has side effects: network_io, database_io, writes_log, modifies_container, modifies_state", "Function get_tracked_files complexity analysis: 15 total connections", "Potential issues detected: IndexError, KeyError, AttributeError", "Function get_tracked_files architectural role: core", "High criticality function requiring careful maintenance"], "open_issues": ["Low confidence analysis for repo.get_tracked_files", "Needs deeper investigation"], "suggested_next_steps": []}}, "global_insights": ["Function cmd_add has 15 dependencies", "Has side effects: modifies_state, modifies_file, network_io, writes_log", "Function format_chat_chunks complexity analysis: 20 total connections", "Function get_symbol_references_between_files complexity analysis: 17 total connections", "Potential issues detected: TypeError, ImportError, AttributeError", "Function get_announcements complexity analysis: 15 total connections", "Potential issues detected: KeyError, IndexError, TypeError, ZeroDivisionError, ValueError, AttributeError", "Potential issues detected: IndexError, KeyError, AttributeError", "Function process_context_requests has 15 dependencies", "Function parse_context_request complexity analysis: 19 total connections", "Function cmd_add complexity analysis: 16 total connections", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Has side effects: network_io, database_io, writes_log, modifies_file, modifies_state", "Has side effects: network_io, database_io, modifies_global, modifies_state", "Function cmd_tokens has 15 dependencies", "Has side effects: network_io, database_io, modifies_container, modifies_state", "Function process_file_requests complexity analysis: 19 total connections", "Function process_map_requests complexity analysis: 18 total connections", "Function get_symbol_references_between_files has 15 dependencies", "Function update complexity analysis: 19 total connections", "Has side effects: modifies_state, modifies_container, network_io, writes_log", "Has side effects: network_io, writes_log, modifies_container, modifies_state", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, ValueError, AttributeError", "Has side effects: modifies_container, network_io, modifies_state", "Function get_file_mentions has 12 dependencies", "Function process_context_request has 9 dependencies", "Function get_repo_map complexity analysis: 17 total connections", "Function get_file_mentions complexity analysis: 15 total connections", "Function __init__ has 15 dependencies", "Function process_context_request complexity analysis: 19 total connections", "Potential issues detected: IndexError, PermissionError, TypeError, ImportError, FileNotFoundError, KeyError, ValueError, AttributeError", "Has side effects: network_io, database_io, writes_log, modifies_container, modifies_state", "Potential issues detected: KeyError, IndexError, TypeError, ImportError, ZeroDivisionError, AttributeError", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, AttributeError", "Potential issues detected: IndexError, TypeError, KeyError, AttributeError", "Function get_repo_overview has 9 dependencies", "Potential issues detected: IndexError, TypeError, ImportError, KeyError, ValueError, AttributeError", "Function process_context_requests complexity analysis: 22 total connections", "Has side effects: network_io, writes_log", "Potential issues detected: ZeroDivisionError, IndexError, TypeError, KeyError, AttributeError", "Function add_requested_file has 9 dependencies", "Function add_requested_file complexity analysis: 14 total connections", "Function get_tracked_files has 10 dependencies", "Has side effects: modifies_state, network_io, database_io, writes_log", "Function get_tracked_files complexity analysis: 15 total connections", "Function get_announcements has 9 dependencies", "Function parse_context_request has 13 dependencies", "Potential issues detected: I<PERSON>rtError, TypeError, KeyError, AttributeError", "Function format_files_for_input complexity analysis: 11 total connections", "Has side effects: modifies_state, network_io, writes_log", "Has side effects: modifies_state, modifies_file, network_io, modifies_container", "Function process_context_requests has 13 dependencies", "Function format_chat_chunks has 14 dependencies", "Function update has 9 dependencies", "Potential issues detected: IndexError, ImportError, TypeError, KeyError, ValueError, AttributeError", "Has side effects: network_io, database_io, writes_log, modifies_state", "Function get_repo_overview complexity analysis: 17 total connections", "Has side effects: network_io, modifies_state", "Function get_repo_map has 7 dependencies", "Function cmd_tokens complexity analysis: 16 total connections", "Potential issues detected: IndexError, TypeError, KeyError, ZeroDivisionError, AttributeError", "Function __init__ complexity analysis: 19 total connections", "Function format_files_for_input has 9 dependencies", "Function process_context_requests complexity analysis: 24 total connections", "Function process_map_requests has 12 dependencies", "Function process_file_requests has 15 dependencies"], "analysis_summary": {"total_entities_analyzed": 29, "total_iterations": 12, "low_confidence_count": 29, "high_confidence_count": 0, "global_insights_count": 97, "last_analysis": "2025-05-28T23:09:53.838886+00:00"}, "confidence_summary": {"average_confidence": 0.32132183908045975, "min_confidence": 0.2866666666666667, "max_confidence": 0.4366666666666667, "low_confidence_count": 29, "total_entities": 29}, "iteration_history": [{"iteration": 1, "timestamp": "2025-05-28T23:09:53.754128+00:00", "entities_count": 26, "confidence": 0.3178205128205128, "status": "continue"}, {"iteration": 2, "timestamp": "2025-05-28T23:09:53.838886+00:00", "entities_count": 26, "confidence": 0.3178205128205128, "status": "continue"}], "analysis_metadata": {"project_path": "C:\\Users\\<USER>\\Documents\\aider_project\\aider__500", "max_tokens": 4000, "engine_version": "IAA Protocol v1.0", "analysis_type": "multi_turn_reasoning"}}}