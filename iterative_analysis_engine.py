#!/usr/bin/env python3
"""
Iterative Analysis Engine (IAA Protocol)

This module implements the Multi-Turn Reasoning Loop that builds understanding
across multiple iterations using the Intelligent Context Selection Engine.

Key Features:
- Progressive Understanding: Build complex understanding over multiple turns
- Context Memory: Remember previous analysis across iterations
- Intelligent Context Evolution: Use context selector to refine focus areas
- Relationship Mapping: Use dependency graphs to guide exploration
- Confidence Scoring: Track certainty of analysis conclusions
- Adaptive Strategy: Adjust analysis approach based on accumulated knowledge
"""

import json
import time
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
from pathlib import Path


class AnalysisConfidence(Enum):
    """Confidence levels for analysis results."""
    VERY_LOW = 0.2
    LOW = 0.4
    MEDIUM = 0.6
    HIGH = 0.8
    VERY_HIGH = 0.95


@dataclass
class EntityAnalysisMemory:
    """Memory for a specific entity across analysis iterations."""
    entity_id: str
    last_confidence: float = 0.0
    iterations: int = 0
    last_analysis_time: str = ""
    summary: str = ""
    insights: List[str] = field(default_factory=list)
    open_issues: List[str] = field(default_factory=list)
    suggested_next_steps: List[str] = field(default_factory=list)
    context_usage_count: int = 0
    was_useful: bool = True


@dataclass
class AnalysisResult:
    """Result of a single analysis iteration."""
    task: str
    iteration: int
    timestamp: str
    entities_analyzed: List[str]
    confidence_scores: Dict[str, float]
    insights: Dict[str, List[str]]
    open_issues: Dict[str, List[str]]
    overall_confidence: float
    next_focus_areas: List[str]
    completion_status: str  # "continue", "complete", "needs_clarification"


class AnalysisMemory:
    """
    Persistent memory system for tracking analysis across iterations.
    
    Maintains entity-specific memory and global analysis state to enable
    progressive understanding building.
    """
    
    def __init__(self):
        """Initialize the analysis memory system."""
        self.entity_memories: Dict[str, EntityAnalysisMemory] = {}
        self.global_insights: List[str] = []
        self.analysis_history: List[AnalysisResult] = []
        self.low_confidence_entities: Set[str] = set()
        self.high_confidence_entities: Set[str] = set()
        self.task_context: Dict[str, Any] = {}
        
    def get_entity_memory(self, entity_id: str) -> EntityAnalysisMemory:
        """Get or create memory for a specific entity."""
        if entity_id not in self.entity_memories:
            self.entity_memories[entity_id] = EntityAnalysisMemory(entity_id=entity_id)
        return self.entity_memories[entity_id]
    
    def update_entity_analysis(self, entity_id: str, confidence: float, 
                             insights: List[str], issues: List[str]):
        """Update analysis results for an entity."""
        memory = self.get_entity_memory(entity_id)
        memory.last_confidence = confidence
        memory.iterations += 1
        memory.last_analysis_time = datetime.now(timezone.utc).isoformat()
        memory.insights.extend(insights)
        memory.open_issues.extend(issues)
        
        # Update confidence tracking
        if confidence < 0.6:
            self.low_confidence_entities.add(entity_id)
            self.high_confidence_entities.discard(entity_id)
        elif confidence > 0.8:
            self.high_confidence_entities.add(entity_id)
            self.low_confidence_entities.discard(entity_id)
    
    def mark_entity_usage(self, entity_id: str, was_useful: bool):
        """Mark whether an entity was useful in the current iteration."""
        memory = self.get_entity_memory(entity_id)
        memory.context_usage_count += 1
        memory.was_useful = was_useful
    
    def get_historical_relevance_score(self, entity_id: str) -> float:
        """Calculate historical relevance score for context selection."""
        memory = self.entity_memories.get(entity_id)
        if not memory:
            return 0.0
        
        # Score based on usage and usefulness
        if memory.context_usage_count > 0 and memory.was_useful:
            return 2.0  # Previously included and useful
        elif memory.context_usage_count > 0 and not memory.was_useful:
            return 0.0  # Previously included but unused
        else:
            return 0.0  # Never included
    
    def get_confidence_gap_entities(self) -> List[str]:
        """Get entities that need more analysis due to low confidence."""
        return list(self.low_confidence_entities)
    
    def add_analysis_result(self, result: AnalysisResult):
        """Add a completed analysis result to history."""
        self.analysis_history.append(result)
        
        # Update global insights
        for entity_insights in result.insights.values():
            self.global_insights.extend(entity_insights)
    
    def get_analysis_summary(self) -> Dict[str, Any]:
        """Get a summary of the current analysis state."""
        return {
            "total_entities_analyzed": len(self.entity_memories),
            "total_iterations": len(self.analysis_history),
            "low_confidence_count": len(self.low_confidence_entities),
            "high_confidence_count": len(self.high_confidence_entities),
            "global_insights_count": len(self.global_insights),
            "last_analysis": self.analysis_history[-1].timestamp if self.analysis_history else None
        }


class ConfidenceTracker:
    """
    Tracks confidence levels and identifies areas needing more analysis.
    
    Uses multiple factors to assess confidence in analysis results and
    guide future iteration focus.
    """
    
    def __init__(self):
        """Initialize the confidence tracker."""
        self.entity_confidences: Dict[str, float] = {}
        self.confidence_factors: Dict[str, Dict[str, float]] = {}
        self.confidence_threshold = 0.7
        
    def calculate_entity_confidence(self, entity_id: str, analysis_data: Dict[str, Any]) -> float:
        """
        Calculate confidence score for an entity based on multiple factors.
        
        Args:
            entity_id: The entity being analyzed
            analysis_data: Data about the entity and its analysis
            
        Returns:
            Confidence score between 0.0 and 1.0
        """
        factors = {}
        
        # Factor 1: Documentation completeness
        doc_coverage = analysis_data.get('doc_coverage', 0.0)
        factors['documentation'] = min(doc_coverage, 1.0)
        
        # Factor 2: Code complexity (inverse relationship)
        complexity = analysis_data.get('complexity', 5)
        factors['complexity'] = max(0.0, 1.0 - (complexity - 5) / 10.0)
        
        # Factor 3: Dependency clarity
        dependency_count = len(analysis_data.get('dependencies', []))
        factors['dependencies'] = 1.0 if dependency_count < 5 else max(0.3, 1.0 - dependency_count / 20.0)
        
        # Factor 4: Analysis depth
        insights_count = len(analysis_data.get('insights', []))
        factors['analysis_depth'] = min(insights_count / 3.0, 1.0)
        
        # Factor 5: Issue resolution
        open_issues = len(analysis_data.get('open_issues', []))
        factors['issue_resolution'] = max(0.0, 1.0 - open_issues / 5.0)
        
        # Weighted average
        weights = {
            'documentation': 0.2,
            'complexity': 0.25,
            'dependencies': 0.2,
            'analysis_depth': 0.2,
            'issue_resolution': 0.15
        }
        
        confidence = sum(factors[factor] * weights[factor] for factor in factors)

        # Ensure confidence is within valid range [0.0, 1.0]
        confidence = max(0.0, min(1.0, confidence))

        # Store factors for debugging
        self.confidence_factors[entity_id] = factors
        self.entity_confidences[entity_id] = confidence

        return confidence
    
    def get_low_confidence_entities(self) -> List[str]:
        """Get entities with confidence below threshold."""
        return [
            entity_id for entity_id, confidence in self.entity_confidences.items()
            if confidence < self.confidence_threshold
        ]
    
    def get_confidence_summary(self) -> Dict[str, Any]:
        """Get summary of confidence analysis."""
        if not self.entity_confidences:
            return {"message": "No confidence data available"}
        
        confidences = list(self.entity_confidences.values())
        return {
            "average_confidence": sum(confidences) / len(confidences),
            "min_confidence": min(confidences),
            "max_confidence": max(confidences),
            "low_confidence_count": len(self.get_low_confidence_entities()),
            "total_entities": len(confidences)
        }


class IterativeAnalysisEngine:
    """
    Main engine for the Iterative Analysis Accumulation (IAA) Protocol.

    This class orchestrates multi-turn reasoning loops that build understanding
    across multiple iterations using intelligent context selection and memory.
    """

    def __init__(self, context_selector, max_iterations: int = 5):
        """
        Initialize the iterative analysis engine.

        Args:
            context_selector: IntelligentContextSelector instance
            max_iterations: Maximum number of analysis iterations
        """
        self.context_selector = context_selector
        self.analysis_memory = AnalysisMemory()
        self.confidence_tracker = ConfidenceTracker()
        self.max_iterations = max_iterations
        self.current_iteration = 0
        self.context_bundle_builder = None  # Lazy initialization

    def analyze_incrementally(self, task: str, task_type: str = "general_analysis",
                            focus_entities: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Perform incremental analysis using the IAA Protocol.

        Args:
            task: Description of the analysis task
            task_type: Type of analysis task
            focus_entities: Optional specific entities to focus on

        Returns:
            Complete analysis results with all iterations
        """
        print(f"🧠 Starting Iterative Analysis: {task}")
        print(f"   Max iterations: {self.max_iterations}")

        # Initialize analysis context
        self.analysis_memory.task_context = {
            "task": task,
            "task_type": task_type,
            "focus_entities": focus_entities or [],
            "start_time": datetime.now(timezone.utc).isoformat()
        }

        all_results = []
        overall_confidence = 0.0

        for iteration in range(1, self.max_iterations + 1):
            self.current_iteration = iteration
            print(f"\n🔄 Iteration {iteration}/{self.max_iterations}")

            # Perform single iteration
            result = self._perform_iteration(task, task_type, focus_entities, iteration)
            all_results.append(result)

            # Update overall confidence
            overall_confidence = result.overall_confidence

            # Check if analysis is complete
            if result.completion_status == "complete":
                print(f"✅ Analysis complete after {iteration} iterations")
                break
            elif result.completion_status == "needs_clarification":
                print(f"❓ Analysis needs clarification after {iteration} iterations")
                break
            elif iteration == self.max_iterations:
                print(f"⏰ Reached maximum iterations ({self.max_iterations})")
                break
            else:
                print(f"🔄 Continuing to iteration {iteration + 1}")
                # Update focus for next iteration
                focus_entities = result.next_focus_areas

        # Compile final results
        final_result = self._compile_final_results(all_results, overall_confidence)

        print(f"🎯 Final Analysis Complete:")
        print(f"   Total iterations: {len(all_results)}")
        print(f"   Overall confidence: {overall_confidence:.2f}")
        print(f"   Entities analyzed: {len(final_result['entity_summaries'])}")

        return final_result

    def _perform_iteration(self, task: str, task_type: str, focus_entities: Optional[List[str]],
                          iteration: int) -> AnalysisResult:
        """Perform a single analysis iteration."""
        print(f"   🎯 Selecting context for iteration {iteration}")

        # Get enhanced context selection with memory integration
        context_bundle = self._get_enhanced_context(task, task_type, focus_entities)

        print(f"   📊 Selected {len(context_bundle.entities)} entities")
        print(f"   🎯 Token usage: {context_bundle.total_tokens}")

        # Simulate analysis (in real implementation, this would call LLM)
        analysis_results = self._simulate_analysis(context_bundle, iteration)

        # Update memory with results
        self._update_memory_with_results(analysis_results)

        # Create iteration result
        result = AnalysisResult(
            task=task,
            iteration=iteration,
            timestamp=datetime.now(timezone.utc).isoformat(),
            entities_analyzed=[f"{entity.module_name}.{entity.entity_name}" for entity in context_bundle.entities],
            confidence_scores=analysis_results["confidence_scores"],
            insights=analysis_results["insights"],
            open_issues=analysis_results["open_issues"],
            overall_confidence=analysis_results["overall_confidence"],
            next_focus_areas=analysis_results["next_focus_areas"],
            completion_status=analysis_results["completion_status"]
        )

        # Add to memory
        self.analysis_memory.add_analysis_result(result)

        return result

    def _get_context_bundle_builder(self):
        """Get the ContextBundleBuilder, initializing it if necessary."""
        if self.context_bundle_builder is None:
            try:
                from context_bundle_builder import ContextBundleBuilder

                # Get IR data from the context selector
                ir_data = self.context_selector.ir_data

                # Create the context bundle builder
                self.context_bundle_builder = ContextBundleBuilder(
                    ir_data=ir_data,
                    analysis_memory=self.analysis_memory,
                    confidence_tracker=self.confidence_tracker,
                    token_budget=self.context_selector.max_tokens
                )
                print("✅ ContextBundleBuilder initialized")

            except ImportError as e:
                print(f"⚠️ Could not import ContextBundleBuilder: {e}")
                self.context_bundle_builder = None
            except Exception as e:
                print(f"⚠️ Error initializing ContextBundleBuilder: {e}")
                self.context_bundle_builder = None

        return self.context_bundle_builder

    def _get_enhanced_context(self, task: str, task_type: str, focus_entities: Optional[List[str]]):
        """Get context selection enhanced with memory and confidence tracking."""
        # Enhance focus entities with memory-driven suggestions
        enhanced_focus = self._enhance_focus_with_memory(focus_entities)

        # Try to use ContextBundleBuilder for enhanced context selection
        bundle_builder = self._get_context_bundle_builder()

        if bundle_builder is not None:
            print(f"   🏗️ Using ContextBundleBuilder for enhanced context selection")

            # Use the enhanced context bundle builder
            enhanced_bundle = bundle_builder.build(
                task=task,
                task_type=task_type,
                focus_entities=enhanced_focus
            )

            # Convert enhanced bundle to compatible format
            context_bundle = self._convert_enhanced_bundle_to_context_bundle(enhanced_bundle)

        else:
            print(f"   🎯 Using standard IntelligentContextSelector")

            # Fall back to standard context selector
            from intelligent_context_selector import TaskType

            # Map string task type to enum
            task_type_map = {
                'debugging': TaskType.DEBUGGING,
                'feature_development': TaskType.FEATURE_DEVELOPMENT,
                'code_review': TaskType.CODE_REVIEW,
                'refactoring': TaskType.REFACTORING,
                'documentation': TaskType.DOCUMENTATION,
                'testing': TaskType.TESTING,
                'general_analysis': TaskType.GENERAL_ANALYSIS
            }

            task_enum = task_type_map.get(task_type.lower(), TaskType.GENERAL_ANALYSIS)

            # Get context bundle from selector
            context_bundle = self.context_selector.select_optimal_context(
                task_description=task,
                task_type=task_enum,
                focus_entities=enhanced_focus
            )

        return context_bundle

    def _convert_enhanced_bundle_to_context_bundle(self, enhanced_bundle):
        """Convert enhanced context bundle to standard context bundle format."""
        # Import here to avoid circular imports
        from intelligent_context_selector import ContextEntity, ContextBundle

        # Convert enhanced entities to standard entities
        standard_entities = []
        for enhanced_entity in enhanced_bundle.context_bundle:
            # Create a standard ContextEntity with all required parameters
            standard_entity = ContextEntity(
                module_name=enhanced_entity.module_name,
                entity_name=enhanced_entity.entity_name,
                entity_type=enhanced_entity.type,
                file_path=enhanced_entity.file,
                criticality=enhanced_entity.criticality,
                change_risk=enhanced_entity.change_risk,
                used_by=enhanced_entity.used_by,
                calls=enhanced_entity.calls,
                side_effects=enhanced_entity.side_effects,
                errors=enhanced_entity.errors
            )
            standard_entities.append(standard_entity)

        # Convert task type string to enum
        from intelligent_context_selector import TaskType
        task_type_map = {
            'debugging': TaskType.DEBUGGING,
            'feature_development': TaskType.FEATURE_DEVELOPMENT,
            'code_review': TaskType.CODE_REVIEW,
            'refactoring': TaskType.REFACTORING,
            'documentation': TaskType.DOCUMENTATION,
            'testing': TaskType.TESTING,
            'general_analysis': TaskType.GENERAL_ANALYSIS
        }
        task_type_enum = task_type_map.get(enhanced_bundle.task_type.lower(), TaskType.GENERAL_ANALYSIS)

        # Create standard context bundle
        context_bundle = ContextBundle(
            task_description=enhanced_bundle.task,
            task_type=task_type_enum,
            entities=standard_entities,
            total_tokens=enhanced_bundle.token_estimate,
            selection_rationale=enhanced_bundle.selection_rationale
        )

        return context_bundle

    def _enhance_focus_with_memory(self, focus_entities: Optional[List[str]]) -> List[str]:
        """Enhance focus entities with insights from analysis memory."""
        enhanced_focus = list(focus_entities) if focus_entities else []

        # Add low confidence entities that need more analysis
        low_confidence = self.analysis_memory.get_confidence_gap_entities()
        enhanced_focus.extend(low_confidence[:3])  # Add top 3 low confidence entities

        # Remove duplicates while preserving order
        seen = set()
        result = []
        for entity in enhanced_focus:
            if entity not in seen:
                seen.add(entity)
                result.append(entity)

        return result

    def _simulate_analysis(self, context_bundle, iteration: int) -> Dict[str, Any]:
        """
        Simulate analysis results for the context bundle.

        In a real implementation, this would send the context to an LLM
        and parse the structured response.
        """
        entities = context_bundle.entities
        confidence_scores = {}
        insights = {}
        open_issues = {}

        # Simulate analysis for each entity
        for entity in entities:
            # Create a unique entity ID from module and entity name
            entity_id = f"{entity.module_name}.{entity.entity_name}"

            # Calculate confidence based on entity properties
            analysis_data = {
                'doc_coverage': 0.8 if hasattr(entity, 'docstring') and entity.docstring else 0.2,
                'complexity': len(entity.calls) + len(entity.used_by),
                'dependencies': entity.calls,
                'insights': [f"Analysis insight for {entity_id}"],
                'open_issues': [] if iteration > 2 else [f"Need more analysis of {entity_id}"]
            }

            confidence = self.confidence_tracker.calculate_entity_confidence(entity_id, analysis_data)
            confidence_scores[entity_id] = confidence

            # Generate insights based on entity type and properties
            entity_insights = []
            if entity.entity_type == "function":
                entity_insights.append(f"Function {entity.entity_name} has {len(entity.calls)} dependencies")
                if entity.side_effects:
                    entity_insights.append(f"Has side effects: {', '.join(entity.side_effects)}")
            elif entity.entity_type == "class":
                entity_insights.append(f"Class {entity.entity_name} with {len(entity.calls)} methods")

            insights[entity_id] = entity_insights

            # Generate open issues for low confidence entities
            if confidence < 0.6:
                open_issues[entity_id] = [f"Low confidence analysis for {entity_id}", "Needs deeper investigation"]
            else:
                open_issues[entity_id] = []

        # Calculate overall confidence
        if confidence_scores:
            overall_confidence = sum(confidence_scores.values()) / len(confidence_scores)
        else:
            overall_confidence = 0.5

        # Determine next focus areas
        next_focus_areas = [
            entity_id for entity_id, confidence in confidence_scores.items()
            if confidence < 0.7
        ][:5]  # Top 5 entities needing more analysis

        # Determine completion status
        if overall_confidence > 0.85 and len(next_focus_areas) == 0:
            completion_status = "complete"
        elif overall_confidence < 0.3:
            completion_status = "needs_clarification"
        else:
            completion_status = "continue"

        return {
            "confidence_scores": confidence_scores,
            "insights": insights,
            "open_issues": open_issues,
            "overall_confidence": overall_confidence,
            "next_focus_areas": next_focus_areas,
            "completion_status": completion_status
        }

    def _update_memory_with_results(self, analysis_results: Dict[str, Any]):
        """Update analysis memory with the results from this iteration."""
        confidence_scores = analysis_results["confidence_scores"]
        insights = analysis_results["insights"]
        open_issues = analysis_results["open_issues"]

        for entity_id in confidence_scores:
            confidence = confidence_scores[entity_id]
            entity_insights = insights.get(entity_id, [])
            entity_issues = open_issues.get(entity_id, [])

            # Update entity memory
            self.analysis_memory.update_entity_analysis(
                entity_id, confidence, entity_insights, entity_issues
            )

            # Mark entity as used and useful if it contributed insights
            was_useful = len(entity_insights) > 0 or confidence > 0.7
            self.analysis_memory.mark_entity_usage(entity_id, was_useful)

    def _compile_final_results(self, all_results: List[AnalysisResult],
                              overall_confidence: float) -> Dict[str, Any]:
        """Compile final analysis results from all iterations."""
        # Aggregate all entities analyzed
        all_entities = set()
        for result in all_results:
            all_entities.update(result.entities_analyzed)

        # Create entity summaries
        entity_summaries = {}
        for entity_id in all_entities:
            memory = self.analysis_memory.get_entity_memory(entity_id)
            entity_summaries[entity_id] = {
                "final_confidence": memory.last_confidence,
                "total_iterations": memory.iterations,
                "insights": memory.insights,
                "open_issues": memory.open_issues,
                "suggested_next_steps": memory.suggested_next_steps
            }

        # Aggregate insights across all iterations
        all_insights = []
        for result in all_results:
            for entity_insights in result.insights.values():
                all_insights.extend(entity_insights)

        return {
            "task": self.analysis_memory.task_context["task"],
            "task_type": self.analysis_memory.task_context["task_type"],
            "total_iterations": len(all_results),
            "overall_confidence": overall_confidence,
            "entity_summaries": entity_summaries,
            "global_insights": list(set(all_insights)),  # Remove duplicates
            "analysis_summary": self.analysis_memory.get_analysis_summary(),
            "confidence_summary": self.confidence_tracker.get_confidence_summary(),
            "iteration_history": [
                {
                    "iteration": result.iteration,
                    "timestamp": result.timestamp,
                    "entities_count": len(result.entities_analyzed),
                    "confidence": result.overall_confidence,
                    "status": result.completion_status
                }
                for result in all_results
            ]
        }
